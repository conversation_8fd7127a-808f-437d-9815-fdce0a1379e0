export const getSizeGuidePageTemplateBlocksQuery = /* GraphQL */ `
  query GetSizeGuidePageTemplateBlocks($slug: String!) {
    pageTemplates(filters: { template_name: { eq: $slug } }) {
      documentId
      blocks {
        ... on ComponentSizeGuideSizeGuideHero {
          id
          __typename
          hero {
            id
            store_banner {
              id
              module
              section
              media {
                id
                title
                subtitle
                cta_title
                cta_link
                theme
                desktop_media {
                  alternativeText
                  url
                }
                mobile_media {
                  alternativeText
                  url
                }
                description
                responsive
              }
              index
            }
          }
        }

        ... on ComponentSizeGuideSidebar {
          id
          heading
          __typename
          media {
            id
            title
            subtitle
            cta_title
            cta_link
            desktop_media {
              alternativeText
              url
            }
            mobile_media {
              alternativeText
              url
            }
            description
            responsive
          }
          description
          action_buttons {
            id
            title
            href
          }
        }

        ... on ComponentSizeGuideSizeGuideContent {
          id
          __typename
          product_image_connection {
            nodes {
              alternativeText
              url
            }
          }
          product_image {
            alternativeText
            url
          }
          size_content {
            id
            Cols {
              id
              column_title
              column_row {
                id
                row_item
              }
            }
            table_title
          }
          guide_content
        }
      }
      template_name
      createdAt
      publishedAt
    }
  }
`;
