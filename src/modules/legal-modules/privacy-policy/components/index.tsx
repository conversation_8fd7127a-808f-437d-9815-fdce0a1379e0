import React, { Suspense } from "react";

import dynamic from "next/dynamic";
import { notFound } from "next/navigation";

import CenterBanner from "@modules/common/center-banner";
import {
  DynamicPageBlock,
  DynamicPageProps,
} from "@modules/dynamic-page/utils/types";
import { getTemplateBlocks } from "utils/api/strapi-api";
import { getLegalPageTemplateBlocksQuery } from "utils/strapi-api/legal-page-query";

import LegalPageContent from "./legal-content";

const legal_components = {
  ComponentCommonCenterBanner: {
    FF: CenterBanner,
    Lazy: dynamic(() => import("@modules/common/center-banner")),
  },
  ComponentLegalLegalPageContent: {
    FF: LegalPageContent,
    Lazy: dynamic(() => import("./legal-content")),
  },
};

export const component_map = {
  ...legal_components,
};

type ComponentMap = keyof typeof component_map;

async function DynamicLegalPage(Props: DynamicPageProps) {
  const { page, page_type } = Props;

  const blocks = await getTemplateBlocks(
    Props.template_name,
    getLegalPageTemplateBlocksQuery
  );

  if (blocks.length === 0) {
    notFound();
  }

  let render_block = (block: DynamicPageBlock, index: number) => {
    const ComponentInfo = component_map[block.__typename as ComponentMap];
    if (!ComponentInfo) return null;

    const Component = index < 3 ? ComponentInfo.FF : ComponentInfo.Lazy;

    const common_props = {
      block,
      page,
      page_type,
      isFirst: index === 0,
      height: "336px",
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return <Component key={`block-${index}`} {...(common_props as any)} />;
  };

  const rendered_blocks = blocks.map(render_block);

  return <Suspense>{rendered_blocks}</Suspense>;
}

export default DynamicLegalPage;
