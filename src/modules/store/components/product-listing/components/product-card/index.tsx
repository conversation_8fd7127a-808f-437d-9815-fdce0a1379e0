"use client";

import React, { useMemo, useState } from "react";

import { redirect } from "next/navigation";

import { HttpTypes } from "@medusajs/types";
import {
  Carousel,
  CarouselContent,
  CarouselDots,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@modules/common/carousel";
import LikeButton from "@modules/common/like-button";
import MediaDisplay from "@modules/common/media-display";
import NextLink from "@modules/common/next-link";
import { checkProductInStock } from "@modules/store/utils/helpers";
import Button from "@modules/ui-elements/button";
import Fade from "embla-carousel-fade";
import { addToCart } from "utils/api/server-api/cart";
import { getProductPrice } from "utils/helpers/product-prices";

function ProductCard({ product }: { product: HttpTypes.StoreProduct }) {
  const { title, subtitle, images } = product;
  const defaultVariant = product?.variants?.[0];

  const [isAdding, setIsAdding] = useState(false);

  const { cheapestPrice } = getProductPrice({
    product,
  });

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!defaultVariant?.id) return null;

    setIsAdding(true);

    await addToCart({
      variantId: defaultVariant.id,
      quantity: 1,
    });

    setIsAdding(false);
    redirect("/cart");
  };

  const inStock = useMemo(
    () => (defaultVariant ? checkProductInStock(defaultVariant) : false),
    [defaultVariant]
  );

  return (
    <div
      className="group relative flex h-auto w-full cursor-pointer flex-col items-center justify-start gap-2"
      onClick={(e) => {
        e.preventDefault();
        redirect(
          `/jewellery/${product.handle}?vid=${defaultVariant?.id || ""}`
        );
      }}
    >
      <div className="absolute right-5 top-5 z-50 cursor-pointer opacity-0 transition-all duration-200 group-hover:opacity-100">
        <LikeButton />
      </div>
      <Carousel opts={{ loop: true }} className="relative" plugins={[Fade()]}>
        <CarouselContent>
          {images?.map((image, index) => (
            <CarouselItem key={index}>
              <MediaDisplay src={image.url} />
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselDots
          dotsWrapperClassName="pb-0"
          slidesLength={2}
          className="absolute bottom-5 left-1/2 block h-auto translate-x-[-50%] pt-5 opacity-0 transition-all duration-200 group-hover:opacity-100"
        />

        <CarouselPrevious className="absolute left-4 top-1/2 h-10 w-10 -translate-y-1/2 transform border-0 opacity-0 transition-all duration-200 hover:bg-transparent hover:text-black group-hover:opacity-100" />
        <CarouselNext className="absolute right-4 top-1/2 h-10 w-10 -translate-y-1/2 transform border-0 opacity-0 transition-all duration-200 hover:bg-transparent hover:text-black group-hover:opacity-100" />
      </Carousel>
      <p className="text-center body-copy-semi-bold lg:h-auto">{title}</p>
      {subtitle && (
        <p className="w-2/3 text-center text-xs text-theme-text-secondary body-copy lg:text-sm">
          {subtitle}
        </p>
      )}
      <p className="body-copy-semi-bold">{cheapestPrice?.calculated_price}</p>
      <Button
        className="w-[120px] opacity-0 transition-all duration-200 group-hover:opacity-100"
        disabled={!inStock || !defaultVariant || isAdding}
        onClick={handleAddToCart}
        loading={isAdding}
      >
        Add To Cart
      </Button>
    </div>
  );
}

export default ProductCard;
