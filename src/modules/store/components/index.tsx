import { Suspense } from "react";

import dynamic from "next/dynamic";
import { notFound } from "next/navigation";

import {
  DynamicPageBlock,
  DynamicPageProps,
} from "@modules/dynamic-page/utils/types";
import FFBookAppointmentSection from "@modules/home/<USER>/appointment-section";
import FFBrandTagline from "@modules/home/<USER>/brand-tagline";
import FFCategoryShowcase from "@modules/home/<USER>/category-showcase";
import FFFeaturedInSection from "@modules/home/<USER>/featured-in";
import FFGiftPromotion from "@modules/home/<USER>/gift-promotion";
import FFHeroBanner from "@modules/home/<USER>/hero-banner";
import FFKeyBenefitsBar from "@modules/home/<USER>/key-benefits-bar";
import FFPromotionalBanner from "@modules/home/<USER>/promotional-banner";
import FFPromotionalTagline from "@modules/home/<USER>/promotional-tagline";
import FFSustainableSection from "@modules/home/<USER>/sustainable-section";
import FFTrendingSection from "@modules/home/<USER>/trending-section";
import ProductListingBanner from "@modules/store/components/product-listing-banner";
import RelatedProductSections from "@modules/store/components/related-product-section";
import { getTemplateBlocks } from "utils/api/strapi-api";
import { getStoreTemplateBlocksQuery } from "utils/strapi-api/store-template-query";

import ProductListing from "./product-listing";
import { StorePageParams } from "../utils/types";

const store_components = {
  ComponentHomeHeroBanner: {
    FF: FFHeroBanner,
    Lazy: dynamic(() => import("@modules/home/<USER>/hero-banner")),
  },
  ComponentHomeBrandTagline: {
    FF: FFBrandTagline,
    Lazy: dynamic(() => import("@modules/home/<USER>/brand-tagline")),
  },
  ComponentHomePromotionalBanner: {
    FF: FFPromotionalBanner,
    Lazy: dynamic(() => import("@modules/home/<USER>/promotional-banner")),
  },
  ComponentHomeCategoryShowcase: {
    FF: FFCategoryShowcase,
    Lazy: dynamic(() => import("@modules/home/<USER>/category-showcase")),
  },
  ComponentHomeTrendingSection: {
    FF: FFTrendingSection,
    Lazy: dynamic(() => import("@modules/home/<USER>/trending-section")),
  },
  ComponentHomeGiftPromotions: {
    FF: FFGiftPromotion,
    Lazy: dynamic(() => import("@modules/home/<USER>/gift-promotion")),
  },
  ComponentHomeAppointmentSection: {
    FF: FFBookAppointmentSection,
    Lazy: dynamic(() => import("@modules/home/<USER>/appointment-section")),
  },
  ComponentHomeMayaveFeaturedIn: {
    FF: FFFeaturedInSection,
    Lazy: dynamic(() => import("@modules/home/<USER>/featured-in")),
  },
  ComponentHomePromotionalTagline: {
    FF: FFPromotionalTagline,
    Lazy: dynamic(() => import("@modules/home/<USER>/promotional-tagline")),
  },
  ComponentHomeBenefitsBar: {
    FF: FFKeyBenefitsBar,
    Lazy: dynamic(() => import("@modules/home/<USER>/key-benefits-bar")),
  },
  ComponentStoreProductListingBanner: {
    FF: ProductListingBanner,
    Lazy: dynamic(
      () => import("@modules/store/components/product-listing-banner")
    ),
  },
  ComponentStoreRelatedProductSection: {
    FF: RelatedProductSections,
    Lazy: dynamic(
      () => import("@modules/store/components/related-product-section")
    ),
  },
  ComponentHomeSustainabilitySection: {
    FF: FFSustainableSection,
    Lazy: dynamic(() => import("@modules/home/<USER>/sustainable-section")),
  },
};

export const component_map = {
  ...store_components,
};

async function StoreDynamicPage(
  Props: DynamicPageProps & { queryParams: StorePageParams["searchParams"] }
) {
  const { page, page_type } = Props;

  const blocks = await getTemplateBlocks(
    Props.template_name,
    getStoreTemplateBlocksQuery
  );

  if (blocks.length === 0) {
    notFound();
  }

  let render_block = (block: DynamicPageBlock, index: number) => {
    const ComponentInfo = component_map[block.__typename];
    if (!ComponentInfo) return null;

    const Component = index < 3 ? ComponentInfo.FF : ComponentInfo.Lazy;

    const common_props = {
      block,
      page,
      page_type,
      isFirst: index === 0,
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return <Component key={`block-${index}`} {...(common_props as any)} />;
  };

  const rendered_blocks = blocks.map(render_block);

  const promotionalBlock = blocks.find(
    (item) => item.__typename === "ComponentStorePromotionalBanner"
  );

  rendered_blocks.splice(
    1,
    0,
    <ProductListing
      key="static-block"
      block={promotionalBlock}
      queryParams={Props.queryParams}
    />
  );

  return <Suspense>{rendered_blocks}</Suspense>;
}

export default StoreDynamicPage;
