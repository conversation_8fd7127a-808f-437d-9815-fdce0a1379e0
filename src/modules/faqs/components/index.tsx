import React, { Suspense } from "react";

import dynamic from "next/dynamic";
import { notFound } from "next/navigation";

import CenterBanner from "@modules/common/center-banner";
import {
  DynamicPageBlock,
  DynamicPageProps,
} from "@modules/dynamic-page/utils/types";
import FAQs from "@modules/faqs/components/faq-item";
import { getTemplateBlocks } from "utils/api/strapi-api";
import { getFAQsTemplateBlocksQuery } from "utils/strapi-api/faqs-tempplate-query";

const component_map = {
  ComponentCommonCenterBanner: {
    FF: CenterBanner,
    Lazy: dynamic(() => import("@modules/common/center-banner")),
  },
  ComponentFaQsFaq: {
    FF: FAQs,
    Lazy: dynamic(() => import("@modules/faqs/components/faq-item")),
  },
};

type ComponentMap = keyof typeof component_map;

async function FAQsDynamicPage(Props: DynamicPageProps) {
  const { page, page_type } = Props;

  const blocks = await getTemplateBlocks(
    Props.template_name,
    getFAQsTemplateBlocksQuery
  );

  if (blocks.length === 0) {
    notFound();
  }

  let render_block = (block: DynamicPageBlock, index: number) => {
    const ComponentInfo = component_map?.[block?.__typename as ComponentMap];
    if (!ComponentInfo) return null;

    const Component = index < 3 ? ComponentInfo.FF : ComponentInfo.Lazy;

    const common_props = {
      block,
      page,
      page_type,
      isFirst: index === 0,
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return <Component key={`block-${index}`} {...(common_props as any)} />;
  };

  const rendered_blocks = blocks.map(render_block);

  return <Suspense>{rendered_blocks}</Suspense>;
}

export default FAQsDynamicPage;
