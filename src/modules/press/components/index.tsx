import { Suspense } from "react";

import dynamic from "next/dynamic";
import { notFound } from "next/navigation";

import CenterBanner from "@modules/common/center-banner";
import {
  DynamicPageBlock,
  DynamicPageProps,
} from "@modules/dynamic-page/utils/types";
import KeyBenefitsBar from "@modules/home/<USER>/key-benefits-bar";
import PromotionalTagline from "@modules/home/<USER>/promotional-tagline";
import PressArticleCards from "@modules/press/components/press-cards";
import { getTemplateBlocks } from "utils/api/strapi-api";
import { getPressTemplateBlocksQuery } from "utils/strapi-api/press-template-query";

import PressHero from "./press-hero";

const press_components = {
  ComponentPressPressHero: {
    FF: PressHero,
    Lazy: dynamic(() => import("@modules/press/components/press-hero")),
  },
  ComponentHomePromotionalTagline: {
    FF: PromotionalTagline,
    Lazy: dynamic(() => import("@modules/home/<USER>/promotional-tagline")),
  },
  ComponentHomeBenefitsBar: {
    FF: KeyBenefitsBar,
    Lazy: dynamic(() => import("@modules/home/<USER>/key-benefits-bar")),
  },
  ComponentPressPressArticleCards: {
    FF: PressArticleCards,
    Lazy: dynamic(() => import("@modules/press/components/press-cards")),
  },
};

export const component_map = {
  ...press_components,
};

type ComponentMap = keyof typeof component_map;

async function DynamicPressPage(Props: DynamicPageProps) {
  const { page, page_type } = Props;

  const blocks = await getTemplateBlocks(
    Props.template_name,
    getPressTemplateBlocksQuery
  );

  if (blocks.length === 0) {
    notFound();
  }

  let render_block = (block: DynamicPageBlock, index: number) => {
    const ComponentInfo = component_map[block.__typename as ComponentMap];
    if (!ComponentInfo) return null;

    const Component = index < 3 ? ComponentInfo.FF : ComponentInfo.Lazy;

    const common_props = {
      block,
      page,
      page_type,
      isFirst: index === 0,
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return <Component key={`block-${index}`} {...(common_props as any)} />;
  };

  const rendered_blocks = blocks.map(render_block);

  return <Suspense>{rendered_blocks}</Suspense>;
}

export default DynamicPressPage;
