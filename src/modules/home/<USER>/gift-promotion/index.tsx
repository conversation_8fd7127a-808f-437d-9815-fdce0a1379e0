import React, { <PERSON> } from "react";

import Link from "next/link";

import Banner from "@modules/common/banner";
import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";
import Button from "@modules/ui-elements/button";

import { BondlessGiftSectionProps } from "./utils/types";
import Text from "@modules/ui-elements/text";

const BondlessGiftSection: FC<
  DynamicPageComponentProps<BondlessGiftSectionProps>
> = ({ block }) => {
  const { gift_promotion } = block;

  return (
    <div className="my-[79px] bg-theme-background-primary content-container md:my-[83px]">
      <div className="flex flex-col gap-8 sm:px-[60px] md:flex-row-reverse md:gap-0 md:px-0">
        <div className="flex-1">
          <Banner
            src={gift_promotion.media[0].desktop_media.url}
            alt={gift_promotion.media[0].title}
            className="h-[790px] md:h-[700px]"
          />
        </div>
        <div className="flex flex-1 flex-col items-center justify-center">
          <div className="max-w-[90%] text-center md:max-w-[373px] md:text-left">
            <Text as="h2" size="headline" className="text-theme-text-primary">
              {gift_promotion.media[0].title}
            </Text>
            <Text
              as="p"
              size="body_large"
              className="text-theme-text-secondary"
            >
              {gift_promotion.media[0].description}
            </Text>
            <Link
              href={gift_promotion.media[0].cta_link || "/"}
              passHref
              className="flex w-full justify-center sm:mt-6 md:justify-start"
            >
              <Button className="h-[38px] w-fit">
                {gift_promotion.media[0].cta_title}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BondlessGiftSection;
