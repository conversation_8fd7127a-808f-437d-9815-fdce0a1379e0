"use client";

import { FC } from "react";

import Image from "next/image";

import {
  Carousel,
  CarouselContent,
  CarouselDots,
  CarouselItem,
} from "@modules/common/carousel";
import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";
import Autoplay from "embla-carousel-autoplay";

import { FeaturedInSectionProps } from "./utils/types";
import Text from "@modules/ui-elements/text";

const FeaturedInSection: FC<
  DynamicPageComponentProps<FeaturedInSectionProps>
> = ({ block }: DynamicPageComponentProps<FeaturedInSectionProps>) => {
  const { mayave_featured_in } = block;

  if (!mayave_featured_in || mayave_featured_in.length === 0) {
    return null;
  }

  return (
    <div
      className="relative mt-[79px] md:mt-[83px]"
      style={{ backgroundColor: "#f5f5f5", color: "#000000" }}
    >
      <Carousel
        plugins={[
          Autoplay({
            delay: 3000,
          }),
        ]}
        opts={{
          align: "start",
        }}
      >
        <CarouselContent>
          {mayave_featured_in.map((item) => {
            return (
              <CarouselItem
                key={item.id}
                className="flex w-full items-center justify-center"
              >
                <div className="mb-[23px] h-full min-w-0 md:mb-[44px] md:w-[529px]">
                  <div className="text-cente mx-1 flex w-full flex-col items-center justify-center gap-2 px-4 pt-[68px] md:px-0 md:pt-20">
                    <Text
                      as="p"
                      size="body_large"
                      className="mx-auto max-w-xl text-center text-[14px] font-light leading-relaxed text-theme-text-primary"
                    >
                      &ldquo;{item.description}&rdquo;
                    </Text>
                    <Image
                      src={item.logo.url}
                      alt={item.logo.alternativeText || item.title}
                      width={100}
                      height={100}
                    />
                  </div>
                </div>
              </CarouselItem>
            );
          })}
        </CarouselContent>
        <CarouselDots
          slidesLength={mayave_featured_in.length}
          className="h-full w-full"
          dotsWrapperClassName="pb-8 md:pb-7"
        />
      </Carousel>
    </div>
  );
};

export default FeaturedInSection;
