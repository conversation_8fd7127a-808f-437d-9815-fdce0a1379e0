import Divider from "@modules/common/divider";
import MediaDisplay from "@modules/common/media-display";
import RichtextBlockRenderer from "@modules/common/richtext-block-renderer";
import { Carousel } from "@modules/home/<USER>/trending-section/components/custom-carousel";
import Text from "@modules/ui-elements/text";

import { ComponentSizeGuideSizeGuideContent } from "../utils/types";
import SizeGuideCategories from "./components/categories";
import SizeTable from "./components/size-table";

function GuideContent({
  guideContent,
  template,
}: {
  guideContent: ComponentSizeGuideSizeGuideContent;
  template: string;
}) {
  const size_content = guideContent?.size_content;

  const product_medias = guideContent?.product_image;

  return (
    <div className="flex w-full flex-col items-start justify-start px-5 pb-5 pt-0 lg:w-[calc(100vw-500px)] lg:p-5">
      <div className="hidden w-full items-center justify-between lg:flex">
        <Text as="p" size="body_semi_bold">
          {template?.toUpperCase()}
        </Text>
      </div>
      <div className="w-full lg:hidden">
        <SizeGuideCategories />
      </div>
      <Divider className="hidden w-full bg-theme-text-secondary lg:block" />
      <div className="flex w-full flex-col items-start justify-start gap-10">
        <div className="flex w-full flex-col items-center justify-start gap-10 lg:flex-row">
          <div className="pt-5 lg:pt-0">
            {product_medias?.length > 0 && (
              <Carousel
                autoPlayInterval={300}
                showDots={false}
                showArrows={true}
                slidesToShow={1}
                centerMode={true}
                autoPlay={product_medias.length > 1}
              >
                {product_medias.map((product, index) => (
                  <MediaDisplay
                    src={product.url}
                    alt={product.alternativeText || ""}
                    className="size-60 object-cover shadow-lg transition-all duration-500"
                    key={`product-${index}`}
                  />
                ))}
              </Carousel>
            )}
          </div>
          {size_content.length > 0 && (
            <div className="flex w-full flex-col items-start justify-start gap-5">
              {size_content.map((table) => {
                return <SizeTable table={table} key={`table-${table.id}`} />;
              })}
            </div>
          )}
        </div>
        {guideContent?.guide_content && (
          <Divider className="w-full bg-theme-text-secondary" />
        )}
        <div className="body-copy">
          <RichtextBlockRenderer content={guideContent?.guide_content || []} />
        </div>
      </div>
    </div>
  );
}

export default GuideContent;
