import React, { Suspense } from "react";

import dynamic from "next/dynamic";
import { notFound } from "next/navigation";

import {
  DynamicPageBlock,
  DynamicPageProps,
} from "@modules/dynamic-page/utils/types";
import { getTemplateBlocks } from "utils/api/strapi-api";
import { getSizeGuidePageTemplateBlocksQuery } from "utils/strapi-api/size-guide-template-query";

import SizeGuideHero from "./hero";
import SizeGuideContent from "./size-guide-content";

const size_guide_components = {
  ComponentSizeGuideSizeGuideHero: {
    FF: SizeGuideHero,
    Lazy: dynamic(() => import("@modules/size-guide/components/hero")),
  },
};

export const component_map = {
  ...size_guide_components,
};

export type ComponentMap = keyof typeof component_map;

async function DynamicSizeGuidePage({
  page,
  page_type,
  template_name,
}: DynamicPageProps) {
  const blocks = await getTemplateBlocks(
    template_name,
    getSizeGuidePageTemplateBlocksQuery
  );

  if (blocks.length === 0) {
    notFound();
  }

  const template = template_name.split("_")[0];

  let render_block = (block: DynamicPageBlock, index: number) => {
    const ComponentInfo = component_map[block.__typename as ComponentMap];
    if (!ComponentInfo) return null;

    const Component = index < 3 ? ComponentInfo.FF : ComponentInfo.Lazy;

    const common_props = {
      block,
      page,
      page_type,
      isFirst: index === 0,
      template,
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return <Component key={`block-${index}`} {...(common_props as any)} />;
  };

  const sidebar = blocks.find(
    (item) => item.__typename === "ComponentSizeGuideSidebar"
  );

  const guideContent = blocks.find(
    (item) => item.__typename === "ComponentSizeGuideSizeGuideContent"
  );

  const rendered_blocks = blocks.map(render_block);

  rendered_blocks.splice(
    1,
    0,
    <SizeGuideContent
      sidebar={sidebar}
      key="static-block"
      guideContent={guideContent}
      template={template}
    />
  );

  return <Suspense>{rendered_blocks}</Suspense>;
}

export default DynamicSizeGuidePage;
