"use client";

import React, { useEffect, useState } from "react";

import MediaDisplay from "@modules/common/media-display";
import NextLink from "@modules/common/next-link";
import cn from "utils/helpers/cn";
import { Media } from "utils/types/common";

import { HeaderDataResponse } from "../../utils/type";

function HeaderLogo({
  header,
  isAccentLogo,
}: {
  header: HeaderDataResponse;
  isAccentLogo: boolean;
}) {
  const [logo, setLogo] = useState<Media>({} as Media);

  useEffect(() => {
    const accentLogo = header?.accent_logo;
    const contrastLogo = header?.contrast_logo;
    if (!accentLogo || !contrastLogo) return;

    setLogo(isAccentLogo ? accentLogo : contrastLogo);
  }, [isAccentLogo, header]);

  return (
    <NextLink href="/" className="z-50 cursor-pointer">
      <MediaDisplay
        className={cn("ml-5 object-cover object-center")}
        src={logo?.url}
        alt={logo?.alternativeText}
        quality={70}
        loadingClassName="w-40 h-16 ml-5"
      />
    </NextLink>
  );
}

export default HeaderLogo;
