"use client";

import React, { useEffect, useState } from "react";

import { MainNavigation } from "@modules/common/header/utils/type";
import NextLink from "@modules/common/next-link";
import Button from "@modules/ui-elements/button";
import { AnimatePresence, motion } from "framer-motion";
import { ChevronDown, ChevronLeft } from "lucide-react";

type MenuNode = {
  title: string;
  slug?: string;
  children?: MenuNode[];
};

// Normalize the menu data to a tree structure to accommodate the nested sub-menu structure
const normalizeMenu = (data: MainNavigation[]): MenuNode[] => {
  return data?.map((main) => ({
    title: main?.title || "",
    slug: main?.slug || "",
    children: main?.sub_menu?.map((sub) => ({
      title: sub?.title || "",
      slug: sub?.slug || "",
      children: sub?.nested_sub_menu?.map((nested) => ({
        title: nested?.title || "",
        slug: nested?.slug || "",
      })),
    })),
  }));
};

/**
 * Renders a mobile navigation link which can either be a button or a link,
 * depending on whether the link has child nodes and/or a slug.
 *
 * @param {Object} props - Component properties.
 * @param {MenuNode} props.link - The menu node data for the link.
 * @param {Function} [props.onNavigate] - Optional callback function to handle navigation for links with children.
 *
 * @returns {JSX.Element | null} - A button or link JSX element, or null if no valid link.
 */

const MobileNavLink = ({
  link,
  onNavigate,
  closeMenu,
}: {
  link: MenuNode;
  onNavigate?: (link: MenuNode) => void;
  closeMenu: () => void;
}) => {
  const hasChildren = link?.children && link?.children?.length > 0;

  if (hasChildren && !link?.slug) {
    return (
      <button
        onClick={() => onNavigate?.(link)}
        className="flex w-full items-center justify-between text-theme-text-primary body-copy"
      >
        <span>{link?.title || ""}</span>
        <motion.div>
          <ChevronDown className="h-4 w-4" />
        </motion.div>
      </button>
    );
  } else if (hasChildren && link?.slug) {
    return (
      <div className="flex w-full items-center justify-between">
        <NextLink
          href={link?.slug || "/"}
          onClick={closeMenu}
          className="text-theme-text-primary body-copy"
        >
          {link?.title || ""}
        </NextLink>
        <Button
          className="w-fit border-0 p-1 hover:bg-transparent hover:text-black"
          variant="primary"
          onClick={(e) => {
            e.preventDefault();
            onNavigate?.(link);
          }}
        >
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>
    );
  } else if (link?.slug) {
    return (
      <NextLink
        href={link?.slug || "/"}
        onClick={closeMenu}
        className="block text-theme-text-primary body-copy"
      >
        {link?.title || ""}
      </NextLink>
    );
  }

  return null;
};

/**
 * A navigation component for mobile devices that takes a list of links and renders a menu.
 *
 * The component uses the `normalizeMenu` function to normalize the menu structure and
 * create a tree of menu nodes. It then uses the `useState` hook to keep track of the current
 * navigation stack.
 *
 * When a link is clicked, the `handleNavigate` function is called, which adds the link's
 * children to the navigation stack. When the back button is clicked, the
 * `handleBack` function is called, which removes the last item from the navigation stack.
 *
 * The component renders a `motion.div` that animates the menu in and out. The
 * `slideVariants` object defines the animation variants for the menu.
 *
 * The component also renders a back button when the navigation stack is more than one
 * item deep.
 */
const MobileNavigation = ({
  links,
  closeMenu,
  isMenuOpen,
}: {
  links: MainNavigation[];
  isMenuOpen: boolean;
  closeMenu: () => void;
}) => {
  const tree = normalizeMenu(links);
  const [navigationStack, setNavigationStack] = useState<MenuNode[][]>([tree]);
  const currentLinks = navigationStack?.[navigationStack?.length - 1];
  const currentDepth = navigationStack?.length - 1;

  const handleNavigate = (link: MenuNode) => {
    if (link?.children && link?.children?.length > 0) {
      setNavigationStack([...navigationStack, link?.children]);
    }
  };

  useEffect(() => {
    if (!isMenuOpen) {
      setNavigationStack([normalizeMenu(links)]);
    }
  }, [isMenuOpen, links]);

  const handleBack = () => {
    if (navigationStack?.length > 1) {
      setNavigationStack(navigationStack?.slice(0, -1));
    }
  };

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? "100%" : "-100%",
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction: number) => ({
      x: direction < 0 ? "100%" : "-100%",
      opacity: 0,
    }),
  };

  if (!links || links?.length === 0) return null;

  return (
    <div className="relative overflow-hidden">
      <AnimatePresence initial={false} mode="wait">
        <motion.div
          key={currentDepth}
          custom={1}
          variants={slideVariants}
          initial="enter"
          animate="center"
          exit="exit"
          transition={{
            type: "spring",
            stiffness: 280,
            damping: 30,
            duration: 0.2,
          }}
        >
          {currentDepth > 0 && (
            <div className="mb-5 flex items-center">
              <Button
                className="mr-2 w-fit border-0 px-0 py-1 hover:bg-transparent hover:text-black"
                variant="primary"
                onClick={handleBack}
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
              <span className="body-copy-semi-bold">
                {navigationStack?.[currentDepth - 1]?.[0]?.title}
              </span>
            </div>
          )}
          <div className="space-y-5">
            {currentLinks?.map((link) => (
              <MobileNavLink
                key={link?.slug || link?.title}
                link={link}
                onNavigate={handleNavigate}
                closeMenu={closeMenu}
              />
            ))}
          </div>
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default MobileNavigation;
