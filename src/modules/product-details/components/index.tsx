import React, { Suspense } from "react";

import dynamic from "next/dynamic";
import { notFound } from "next/navigation";

import {
  DynamicPageBlock,
  DynamicPageProps,
} from "@modules/dynamic-page/utils/types";
import BondlessGiftSection from "@modules/home/<USER>/gift-promotion";
import KeyBenefitsBar from "@modules/home/<USER>/key-benefits-bar";
import PromotionalTagline from "@modules/home/<USER>/promotional-tagline";
import SustainableSection from "@modules/home/<USER>/sustainable-section";
import ProductDetailsHero from "@modules/product-details/components/hero";
import RelatedProductSections from "@modules/store/components/related-product-section";
import { getTemplateBlocks } from "utils/api/strapi-api";
import { getPDPTemplateBlocksQuery } from "utils/strapi-api/pdp-template-query";

import ProductInfoSection from "./info-section";

const component_map = {
  ComponentPdpProductDetails: {
    FF: ProductDetailsHero,
    Lazy: dynamic(() => import("@modules/product-details/components/hero")),
  },
  ComponentStoreRelatedProductSection: {
    FF: RelatedProductSections,
    Lazy: dynamic(
      () => import("@modules/store/components/related-product-section")
    ),
  },
  ComponentHomePromotionalTagline: {
    FF: PromotionalTagline,
    Lazy: dynamic(() => import("@modules/home/<USER>/promotional-tagline")),
  },
  ComponentHomeBenefitsBar: {
    FF: KeyBenefitsBar,
    Lazy: dynamic(() => import("@modules/home/<USER>/key-benefits-bar")),
  },
  ComponentHomeGiftPromotions: {
    FF: BondlessGiftSection,
    Lazy: dynamic(() => import("@modules/home/<USER>/gift-promotion")),
  },
  ComponentHomeSustainabilitySection: {
    FF: SustainableSection,
    Lazy: dynamic(() => import("@modules/home/<USER>/sustainable-section")),
  },
};

type ComponentMap = keyof typeof component_map;

async function DynamicProductDetails({
  page,
  page_type,
  template_name,
}: DynamicPageProps) {
  const blocks = await getTemplateBlocks(
    template_name,
    getPDPTemplateBlocksQuery
  );

  if (blocks.length === 0) {
    notFound();
  }

  let render_block = (block: DynamicPageBlock, index: number) => {
    const ComponentInfo = component_map?.[block?.__typename as ComponentMap];
    if (!ComponentInfo) return null;

    const Component = index < 3 ? ComponentInfo.FF : ComponentInfo.Lazy;

    const common_props = {
      block,
      page,
      page_type,
      isFirst: index === 0,
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return <Component key={`block-${index}`} {...(common_props as any)} />;
  };

  const heroBlock = blocks.find(
    (item) => item.__typename === "ComponentPdpProductDetails"
  );

  const rendered_blocks = blocks.map(render_block);

  rendered_blocks.splice(
    1,
    0,
    <ProductInfoSection block={heroBlock} key="static-block" />
  );

  return <Suspense>{rendered_blocks}</Suspense>;
}

export default DynamicProductDetails;
