import React from "react";

import dynamic from "next/dynamic";
import { notFound } from "next/navigation";

import CollectionDetails from "@modules/collections/components/collection-details";
import CollectionShowcase from "@modules/collections/components/collection-showcase";
import PressAndHold from "@modules/collections/components/press-and-hold";
import {
  DynamicPageBlock,
  DynamicPageProps,
} from "@modules/dynamic-page/utils/types";
import ProductListing from "@modules/store/components/product-listing";
import { getTemplateBlocks } from "utils/api/strapi-api";
import { getCollectionPLPTemplateBlocksQuery } from "utils/strapi-api/collection-plp-template-query";

import HeroMediaCenterBanner from "./hero-media-center-banner";

const collection_plp_components = {
  ComponentCollectionPlpHeroCenterBanner: {
    FF: HeroMediaCenterBanner,
    Lazy: dynamic(
      () => import("@modules/collections/components/hero-media-center-banner")
    ),
  },
  ComponentCollectionPlpCollectionShowcase: {
    FF: CollectionShowcase,
    Lazy: dynamic(
      () => import("@modules/collections/components/collection-showcase")
    ),
  },
  ComponentCollectionPlpPressHold: {
    FF: PressAndHold,
    Lazy: dynamic(
      () => import("@modules/collections/components/press-and-hold")
    ),
  },
  ComponentCollectionPlpCollectionDetails: {
    FF: CollectionDetails,
    Lazy: dynamic(
      () => import("@modules/collections/components/collection-details")
    ),
  },
};

const component_map = {
  ...collection_plp_components,
};

type ComponentMap = keyof typeof component_map;

async function DynamicCollections({
  page,
  page_type,
  template_name,
}: DynamicPageProps) {
  const blocks = await getTemplateBlocks(
    template_name,
    getCollectionPLPTemplateBlocksQuery
  );

  if (blocks.length === 0) {
    notFound();
  }

  let render_block = (block: DynamicPageBlock, index: number) => {
    const ComponentInfo = component_map?.[block?.__typename as ComponentMap];
    if (!ComponentInfo) return null;

    const Component = index < 3 ? ComponentInfo.FF : ComponentInfo.Lazy;

    const common_props = {
      block,
      page,
      page_type,
      isFirst: index === 0,
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return <Component key={`block-${index}`} {...(common_props as any)} />;
  };

  const promotionalBlock = blocks.find(
    (item) => item.__typename === "ComponentStorePromotionalBanner"
  );

  const rendered_blocks = blocks.map(render_block);

  rendered_blocks.push(
    <ProductListing key="static-block" block={promotionalBlock} />
  );

  return <>{rendered_blocks}</>;
}

export default DynamicCollections;
