"use client";

import { useState } from "react";

import { PressHoldType } from "@modules/collections/utils/types";
import Banner from "@modules/common/banner";
import MediaDisplay from "@modules/common/media-display";
import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";

const PressAndHold = ({ block }: DynamicPageComponentProps<PressHoldType>) => {
  const pressHoldSection = block?.press_and_hold_section || {};
  const [isHolding, setIsHolding] = useState(false);

  const handleHoldStart = () => {
    setTimeout(() => {
      setIsHolding(true);
    }, 200);
  };
  const handleHoldEnd = () => setIsHolding(false);

  return (
    <div className="h-[600px] w-full bg-theme-background-accent">
      <Banner
        src={pressHoldSection?.background_video?.url}
        alt={pressHoldSection?.background_video?.alternativeText}
        className="h-full w-full px-5 py-10 md:px-[189px] md:py-[75px]"
        autoPlay={isHolding}
        content={
          <div className="absolute bottom-[15%] w-full text-theme-text-contrast">
            <div
              className="flex cursor-pointer flex-col items-center justify-center gap-3 text-center text-black content-container"
              onMouseDown={handleHoldStart}
              onMouseUp={handleHoldEnd}
              onMouseLeave={handleHoldEnd}
              onTouchStart={handleHoldStart}
              onTouchEnd={handleHoldEnd}
            >
              <span className="text-theme-text-primary body-copy">
                {pressHoldSection?.title}
              </span>
              <div className="flex size-10 items-center justify-center rounded-full bg-[#EBEBEBCC] p-3">
                <MediaDisplay
                  src={
                    pressHoldSection?.[
                      isHolding ? "pressed_icon" : "default_icon"
                    ]?.url
                  }
                />
              </div>
            </div>
          </div>
        }
      />
    </div>
  );
};

export default PressAndHold;
