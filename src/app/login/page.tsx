import React from "react";

import Button from "@modules/ui-elements/button";
import Input from "@modules/ui-elements/input";

function LoginPage() {
  return (
    <div className="flex h-screen w-full items-center justify-center p-20">
      <form className="w-1/2">
        <div className="mb-5">
          <label className="mb-2 block text-sm font-medium">Your email</label>
          <Input
            type="email"
            id="email"
            className="block w-full rounded-lg"
            placeholder="<EMAIL>"
            required
          />
        </div>
        <div className="mb-5">
          <label className="mb-2 block text-sm font-medium">
            Your password
          </label>
          <Input
            type="password"
            id="password"
            placeholder="••••••••"
            className="block w-full rounded-lg text-sm text-gray-900"
            required
          />
        </div>
        <div className="mb-5 flex items-start">
          <div className="flex h-5 items-center">
            <input
              id="remember"
              type="checkbox"
              value=""
              className="focus:ring-3 h-4 w-4 rounded-sm"
              required
            />
          </div>
          <label className="ms-2 text-sm font-medium">Remember me</label>
        </div>
        <Button
          type="submit"
          className="w-full rounded-lg px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 sm:w-auto"
        >
          Submit
        </Button>
      </form>
    </div>
  );
}

export default LoginPage;
