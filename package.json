{"name": "jw-mayave-frontend", "version": "0.1.0", "private": true, "author": "devx commerce", "description": "JW Mayave Frontend", "keywords": ["jw-mayave-frontend"], "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "analyze": "ANALYZE=true next build", "prepare": "husky install"}, "lint-staged": {"*.{js,ts,jsx,tsx,json,css,scss,md,html}": ["prettier --write", "eslint --fix"]}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@medusajs/js-sdk": "^2.8.7", "@medusajs/types": "^2.8.7", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@strapi/blocks-react-renderer": "^1.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-auto-scroll": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-fade": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.0", "lodash": "^4.17.21", "lucide-react": "^0.479.0", "next": "15.1.7", "pg": "^8.11.3", "qs": "^6.12.1", "react": "19.0.0-rc-66855b96-20241106", "react-country-flag": "^3.1.0", "react-dom": "19.0.0-rc-66855b96-20241106", "react-hook-form": "^7.60.0", "server-only": "^0.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tailwindcss-radix": "^4.0.2", "webpack": "^5", "zod": "^4.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.26.0", "@svgr/webpack": "^8.1.0", "@types/lodash": "^4.14.195", "@types/node": "^24.0.10", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "autoprefixer": "^10.4.2", "eslint": "^9.26.0", "eslint-config-next": "15.1.7", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "lint-staged": "^16.0.0", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^3.4.1", "typescript": "^5", "typescript-eslint": "^8.32.0"}, "packageManager": "yarn@3.2.3+sha512.f26f951f67de0c6a33ee381e5ff364709c87e70eb5e65c694e4facde3512f1fa80b8679e6ba31ce7d340fbb46f08dd683af9457e240f25a204be7427940d767e"}