# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@alloc/quick-lru@npm:^5.2.0":
  version: 5.2.0
  resolution: "@alloc/quick-lru@npm:5.2.0"
  checksum: bdc35758b552bcf045733ac047fb7f9a07c4678b944c641adfbd41f798b4b91fffd0fdc0df2578d9b0afc7b4d636aa6e110ead5d6281a2adc1ab90efd7f057f8
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: d3ad7b89d973df059c4e8e6d7c972cbeb1bb2f18f002a3bd04ae0707da214cb06cc06929b65aa2313b9347463df2914772298bae8b1d7973f246bb3f2ab3e8f0
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": ^7.27.1
    js-tokens: ^4.0.0
    picocolors: ^1.1.1
  checksum: 5874edc5d37406c4a0bb14cf79c8e51ad412fb0423d176775ac14fc0259831be1bf95bdda9c2aa651126990505e09a9f0ed85deaa99893bc316d2682c5115bdc
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.27.2, @babel/compat-data@npm:^7.27.7, @babel/compat-data@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/compat-data@npm:7.28.0"
  checksum: 37a40d4ea10a32783bc24c4ad374200f5db864c8dfa42f82e76f02b8e84e4c65e6a017fc014d165b08833f89333dff4cb635fce30f03c333ea3525ea7e20f0a2
  languageName: node
  linkType: hard

"@babel/core@npm:^7.21.3":
  version: 7.28.0
  resolution: "@babel/core@npm:7.28.0"
  dependencies:
    "@ampproject/remapping": ^2.2.0
    "@babel/code-frame": ^7.27.1
    "@babel/generator": ^7.28.0
    "@babel/helper-compilation-targets": ^7.27.2
    "@babel/helper-module-transforms": ^7.27.3
    "@babel/helpers": ^7.27.6
    "@babel/parser": ^7.28.0
    "@babel/template": ^7.27.2
    "@babel/traverse": ^7.28.0
    "@babel/types": ^7.28.0
    convert-source-map: ^2.0.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.2
    json5: ^2.2.3
    semver: ^6.3.1
  checksum: 86da9e26c96e22d96deca0509969d273476f61c30464f262dec5e5a163422e07d5ab690ed54619d10fcab784abd10567022ce3d90f175b40279874f5288215e3
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/generator@npm:7.28.0"
  dependencies:
    "@babel/parser": ^7.28.0
    "@babel/types": ^7.28.0
    "@jridgewell/gen-mapping": ^0.3.12
    "@jridgewell/trace-mapping": ^0.3.28
    jsesc: ^3.0.2
  checksum: 3fc9ecca7e7a617cf7b7357e11975ddfaba4261f374ab915f5d9f3b1ddc8fd58da9f39492396416eb08cf61972d1aa13c92d4cca206533c553d8651c2740f07f
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.27.1, @babel/helper-annotate-as-pure@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-annotate-as-pure@npm:7.27.3"
  dependencies:
    "@babel/types": ^7.27.3
  checksum: 63863a5c936ef82b546ca289c9d1b18fabfc24da5c4ee382830b124e2e79b68d626207febc8d4bffc720f50b2ee65691d7d12cc0308679dee2cd6bdc926b7190
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.27.1, @babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": ^7.27.2
    "@babel/helper-validator-option": ^7.27.1
    browserslist: ^4.24.0
    lru-cache: ^5.1.1
    semver: ^6.3.1
  checksum: 7b95328237de85d7af1dea010a4daa28e79f961dda48b652860d5893ce9b136fc8b9ea1f126d8e0a24963b09ba5c6631dcb907b4ce109b04452d34a6ae979807
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-class-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.1
    "@babel/helper-member-expression-to-functions": ^7.27.1
    "@babel/helper-optimise-call-expression": ^7.27.1
    "@babel/helper-replace-supers": ^7.27.1
    "@babel/helper-skip-transparent-expression-wrappers": ^7.27.1
    "@babel/traverse": ^7.27.1
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 406954b455e5b20924e7d1b41cf932e6e98e95c3a5224c7a70c3ad96a84e8fbde915ceff7ddbf9c7d121397c4e9274f061241648475122cf6fe54e0a95caae15
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.1
    regexpu-core: ^6.2.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 2ede6bbad0016a9262fd281ce8f1a5d69e6179dcec4ea282830e924c29a29b66b0544ecb92e4ef4acdaf2c4c990931d7dc442dbcd6a8bcec4bad73923ef70934
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.6.5":
  version: 0.6.5
  resolution: "@babel/helper-define-polyfill-provider@npm:0.6.5"
  dependencies:
    "@babel/helper-compilation-targets": ^7.27.2
    "@babel/helper-plugin-utils": ^7.27.1
    debug: ^4.4.1
    lodash.debounce: ^4.0.8
    resolve: ^1.22.10
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 9fd3b09b209c8ed0d3d8bc1f494f1368b9e1f6e46195af4ce948630fe97d7dafde4882eedace270b319bf6555ddf35e220c77505f6d634f621766cdccbba0aae
  languageName: node
  linkType: hard

"@babel/helper-globals@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/helper-globals@npm:7.28.0"
  checksum: d8d7b91c12dad1ee747968af0cb73baf91053b2bcf78634da2c2c4991fb45ede9bd0c8f9b5f3254881242bc0921218fcb7c28ae885477c25177147e978ce4397
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-member-expression-to-functions@npm:7.27.1"
  dependencies:
    "@babel/traverse": ^7.27.1
    "@babel/types": ^7.27.1
  checksum: b13a3d120015a6fd2f6e6c2ff789cd12498745ef028710cba612cfb751b91ace700c3f96c1689228d1dcb41e9d4cf83d6dff8627dcb0c8da12d79440e783c6b8
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": ^7.27.1
    "@babel/types": ^7.27.1
  checksum: 92d01c71c0e4aacdc2babce418a9a1a27a8f7d770a210ffa0f3933f321befab18b655bc1241bebc40767516731de0b85639140c42e45a8210abe1e792f115b28
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.1, @babel/helper-module-transforms@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-module-transforms@npm:7.27.3"
  dependencies:
    "@babel/helper-module-imports": ^7.27.1
    "@babel/helper-validator-identifier": ^7.27.1
    "@babel/traverse": ^7.27.3
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: c611d42d3cb7ba23b1a864fcf8d6cde0dc99e876ca1c9a67e4d7919a70706ded4aaa45420de2bf7f7ea171e078e59f0edcfa15a56d74b9485e151b95b93b946e
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-optimise-call-expression@npm:7.27.1"
  dependencies:
    "@babel/types": ^7.27.1
  checksum: 0fb7ee824a384529d6b74f8a58279f9b56bfe3cce332168067dddeab2552d8eeb56dc8eaf86c04a3a09166a316cb92dfc79c4c623cd034ad4c563952c98b464f
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 5d715055301badab62bdb2336075a77f8dc8bd290cad2bc1b37ea3bf1b3efc40594d308082229f239deb4d6b5b80b0a73bce000e595ea74416e0339c11037047
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-remap-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.1
    "@babel/helper-wrap-function": ^7.27.1
    "@babel/traverse": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 0747397ba013f87dbf575454a76c18210d61c7c9af0f697546b4bcac670b54ddc156330234407b397f0c948738c304c228e0223039bc45eab4fbf46966a5e8cc
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-replace-supers@npm:7.27.1"
  dependencies:
    "@babel/helper-member-expression-to-functions": ^7.27.1
    "@babel/helper-optimise-call-expression": ^7.27.1
    "@babel/traverse": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 3690266c304f21008690ba68062f889a363583cabc13c3d033b94513953147af3e0a3fdb48fa1bb9fa3734b64e221fc65e5222ab70837f02321b7225f487c6ef
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.27.1"
  dependencies:
    "@babel/traverse": ^7.27.1
    "@babel/types": ^7.27.1
  checksum: 4f380c5d0e0769fa6942a468b0c2d7c8f0c438f941aaa88f785f8752c103631d0904c7b4e76207a3b0e6588b2dec376595370d92ca8f8f1b422c14a69aa146d4
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 0a8464adc4b39b138aedcb443b09f4005d86207d7126e5e079177e05c3116107d856ec08282b365e9a79a9872f40f4092a6127f8d74c8a01c1ef789dacfc25d6
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 3c7e8391e59d6c85baeefe9afb86432f2ab821c6232b00ea9082a51d3e7e95a2f3fb083d74dc1f49ac82cf238e1d2295dafcb001f7b0fab479f3f56af5eaaa47
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: db73e6a308092531c629ee5de7f0d04390835b21a263be2644276cb27da2384b64676cab9f22cd8d8dbd854c92b1d7d56fc8517cf0070c35d1c14a8c828b0903
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-wrap-function@npm:7.27.1"
  dependencies:
    "@babel/template": ^7.27.1
    "@babel/traverse": ^7.27.1
    "@babel/types": ^7.27.1
  checksum: b0427765766494cb5455a188d4cdef5e6167f2835a8ed76f3c25fa3bbe2ec2a716588fa326c52fab0d184a9537200d76e48656e516580a914129d74528322821
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.6":
  version: 7.27.6
  resolution: "@babel/helpers@npm:7.27.6"
  dependencies:
    "@babel/template": ^7.27.2
    "@babel/types": ^7.27.6
  checksum: 12f96a5800ff677481dbc0a022c617303e945210cac4821ad5377a31201ffd8d9c4d00f039ed1487cf2a3d15868fb2d6cabecdb1aba334bd40a846f1938053a2
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.27.2, @babel/parser@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/parser@npm:7.28.0"
  dependencies:
    "@babel/types": ^7.28.0
  bin:
    parser: ./bin/babel-parser.js
  checksum: 718e4ce9b0914701d6f74af610d3e7d52b355ef1dcf34a7dedc5930e96579e387f04f96187e308e601828b900b8e4e66d2fe85023beba2ac46587023c45b01cf
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/traverse": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 72f24b9487e445fa61cf8be552aad394a648c2bb445c38d39d1df003186d9685b87dd8d388c950f438ea0ca44c82099d9c49252fb681c719cc72edf02bbe0304
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: eb7f4146dc01f1198ce559a90b077e58b951a07521ec414e3c7d4593bf6c4ab5c2af22242a7e9fec085e20299e0ba6ea97f44a45e84ab148141bf9eb959ad25e
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 621cfddfcc99a81e74f8b6f9101fd260b27500cb1a568e3ceae9cc8afe9aee45ac3bca3900a2b66c612b1a2366d29ef67d4df5a1c975be727eaad6906f98c2c6
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-skip-transparent-expression-wrappers": ^7.27.1
    "@babel/plugin-transform-optional-chaining": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: f07aa80272bd7a46b7ba11a4644da6c9b6a5a64e848dfaffdad6f02663adefd512e1aaebe664c4dd95f7ed4f80c872c7f8db8d8e34b47aae0930b412a28711a0
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/traverse": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 4d6792ccade2d6b9d5577b0a879ab22d05ac8a1206b1a636b6ffdb53a0c0bacaf0f7947e46de254f228ffd75456f4b95ccd82fdeaefc0b92d88af3c5991863ad
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2":
  version: 7.21.0-placeholder-for-preset-env.2
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d97745d098b835d55033ff3a7fb2b895b9c5295b08a5759e4f20df325aa385a3e0bc9bd5ad8f2ec554a44d4e6525acfc257b8c5848a1345cb40f26a30e277e91
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fb661d630808d67ecb85eabad25aac4e9696a20464bad4c4a6a0d3d40e4dc22557d47e9be3d591ec06429cf048cfe169b8891c373606344d51c4f3ac0f91d6d0
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 97973982fff1bbf86b3d1df13380567042887c50e2ae13a400d02a8ff2c9742a60a75e279bfb73019e1cd9710f04be5e6ab81f896e6678dcfcec8b135e8896cf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c6d1324cff286a369aa95d99b8abd21dd07821b5d3affd5fe7d6058c84cff9190743287826463ee57a7beecd10fa1e4bc99061df532ee14e188c1c8937b13e3a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 87836f7e32af624c2914c73cd6b9803cf324e07d43f61dbb973c6a86f75df725e12540d91fac7141c14b697aa9268fd064220998daced156e96ac3062d7afb41
  languageName: node
  linkType: hard

"@babel/plugin-syntax-unicode-sets-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-syntax-unicode-sets-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: a651d700fe63ff0ddfd7186f4ebc24447ca734f114433139e3c027bc94a900d013cf1ef2e2db8430425ba542e39ae160c3b05f06b59fd4656273a3df97679e9c
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 62c2cc0ae2093336b1aa1376741c5ed245c0987d9e4b4c5313da4a38155509a7098b5acce582b6781cc0699381420010da2e3086353344abe0a6a0ec38961eb7
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-remap-async-to-generator": ^7.27.1
    "@babel/traverse": ^7.28.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 174aaccd7a8386fd7f32240c3f65a93cf60dcc5f6a2123cfbff44c0d22b424cd41de3a0c6d136b6a2fa60a8ca01550c261677284cb18a0daeab70730b2265f1d
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-remap-async-to-generator": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d79d7a7ae7d416f6a48200017d027a6ba94c09c7617eea8b4e9c803630f00094c1a4fc32bf20ce3282567824ce3fcbda51653aac4003c71ea4e681b331338979
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7fb4988ca80cf1fc8345310d5edfe38e86b3a72a302675cdd09404d5064fe1d1fe1283ebe658ad2b71445ecef857bfb29a748064306b5f6c628e0084759c2201
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-block-scoping@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6d740f9a386e5fbdffd9e7c5a8400bff8d54068241a78b8e71aba6f1f46eff0c4297902f5f1543bee1ed076ec88d0dc4ceed19e98a466802c14d3c20f178f712
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-class-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 475a6e5a9454912fe1bdc171941976ca10ea4e707675d671cdb5ce6b6761d84d1791ac61b6bca81a2e5f6430cb7b9d8e4b2392404110e69c28207a754e196294
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-static-block@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-class-static-block@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: 69688fe1641ae0ea025b916b8c2336e8b5643a5ec292e8f546ecd35d9d9d4bb301d738910822a79d867098cf687d550d92cd906ae4cda03c0f69b1ece2149a58
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-classes@npm:7.28.0"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.3
    "@babel/helper-compilation-targets": ^7.27.2
    "@babel/helper-globals": ^7.28.0
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-replace-supers": ^7.27.1
    "@babel/traverse": ^7.28.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0b47188046a4f1579123354ee30d08874b4b585d45128a3d492fa1cba7e26c8039d8c44d38d85f4eaa9b5a53064c66f032cfc35526c73c74a865a11edf3a0c28
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-computed-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/template": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 48bd20f7d631b08c51155751bf75b698d4a22cca36f41c22921ab92e53039c9ec5c3544e5282e18692325ef85d2e4a18c27e12c62b5e20c26fb0c92447e35224
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-destructuring@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/traverse": ^7.28.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5b464d6a03c6eaa1327b60ffc1630ca977db0256938b34e281e65c81c965680e930a6bac043272942d6d4bbd7d1eddded0b7231779429ba51275e092e7367859
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2173e5b13f403538ffc6bd57b190cedf4caf320abc13a99e5b2721864e7148dbd3bd7c82d92377136af80432818f665fdd9a1fd33bc5549a4c91e24e5ce2413c
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ef2112d658338e3ff0827f39a53c0cfa211f1cbbe60363bca833a5269df389598ec965e7283600b46533c39cdca82307d0d69c0f518290ec5b00bb713044715b
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 2a109613535e6ac79240dced71429e988affd6a5b3d0cd0f563c8d6c208c51ce7bf2c300bc1150502376b26a51f279119b3358f1c0f2d2f8abca3bcd62e1ae46
  languageName: node
  linkType: hard

"@babel/plugin-transform-dynamic-import@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-dynamic-import@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7a9fbc8d17148b7f11a1d1ca3990d2c2cd44bd08a45dcaf14f20a017721235b9044b20e6168b6940282bb1b48fb78e6afbdfb9dd9d82fde614e15baa7d579932
  languageName: node
  linkType: hard

"@babel/plugin-transform-explicit-resource-management@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-explicit-resource-management@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/plugin-transform-destructuring": ^7.28.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a44140097ed4854883c426613f4e8763237cd0fdab1c780514f4315f6c148d6b528d7a57fe6fdec4dbce28a21b70393ef3507b72dfec2e30bfc8d7db1ff19474
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4ff4a0f30babc457a5ae8564deda209599627c2ce647284a0e8e66f65b44f6d968cf77761a4cc31b45b61693f0810479248c79e681681d8ccb39d0c52944c1fd
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 85082923eca317094f08f4953d8ea2a6558b3117826c0b740676983902b7236df1f4213ad844cb38c2dae104753dbe8f1cc51f01567835d476d32f5f544a4385
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-for-of@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-skip-transparent-expression-wrappers": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c9224e08de5d80b2c834383d4359aa9e519db434291711434dd996a4f86b7b664ad67b45d65459b7ec11fa582e3e11a3c769b8a8ca71594bdd4e2f0503f84126
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-function-name@npm:7.27.1"
  dependencies:
    "@babel/helper-compilation-targets": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/traverse": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 26a2a183c3c52a96495967420a64afc5a09f743a230272a131668abf23001e393afa6371e6f8e6c60f4182bea210ed31d1caf866452d91009c1daac345a52f23
  languageName: node
  linkType: hard

"@babel/plugin-transform-json-strings@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-json-strings@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2c05a02f63b49f47069271b3405a66c3c8038de5b995b0700b1bd9a5e2bb3e67abd01e4604629302a521f4d8122a4233944aefa16559fd4373d256cc5d3da57f
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0a76d12ab19f32dd139964aea7da48cecdb7de0b75e207e576f0f700121fe92367d788f328bf4fb44b8261a0f605c97b44e62ae61cddbb67b14e94c88b411f95
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2757955d81d65cc4701c17b83720745f6858f7a1d1d58117e379c204f47adbeb066b778596b6168bdbf4a22c229aab595d79a9abc261d0c6bfd62d4419466e73
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 804121430a6dcd431e6ffe99c6d1fbbc44b43478113b79c677629e7f877b4f78a06b69c6bfb2747fd84ee91879fe2eb32e4620b53124603086cf5b727593ebe8
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-amd@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8bb36d448e438d5d30f4faf19120e8c18aa87730269e65d805bf6032824d175ed738057cc392c2c8a650028f1ae0f346cad8d6b723f31a037b586e2092a7be18
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bc45c1beff9b145c982bd6a614af338893d38bce18a9df7d658c9084e0d8114b286dcd0e015132ae7b15dd966153cb13321e4800df9766d0ddd892d22bf09d2a
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-validator-identifier": ^7.27.1
    "@babel/traverse": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7c17a8973676c18525d87f277944616596f1b154cc2b9263bfd78ecdbf5f4288ec46c7f58017321ca3e3d6dfeb96875467b95311a39719b475d42a157525d87f
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-umd@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b007dd89231f2eeccf1c71a85629bcb692573303977a4b1c5f19a835ea6b5142c18ef07849bc6d752b874a11bc0ddf3c67468b77c8ee8310290b688a4f01ef31
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: a711c92d9753df26cefc1792481e5cbff4fe4f32b383d76b25e36fa865d8023b1b9aa6338cf18f5c0e864c71a7fbe8115e840872ccd61a914d9953849c68de7d
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-new-target@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 32c8078d843bda001244509442d68fd3af088d7348ba883f45c262b2c817a27ffc553b0d78e7f7a763271b2ece7fac56151baad7a91fb21f5bb1d2f38e5acad7
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1c6b3730748782d2178cc30f5cc37be7d7666148260f3f2dfc43999908bdd319bdfebaaf19cf04ac1f9dee0f7081093d3fa730cda5ae1b34bcd73ce406a78be7
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 049b958911de86d32408cd78017940a207e49c054ae9534ab53a32a57122cc592c0aae3c166d6f29bd1a7d75cc779d71883582dd76cb28b2fbb493e842d8ffca
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.28.0"
  dependencies:
    "@babel/helper-compilation-targets": ^7.27.2
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/plugin-transform-destructuring": ^7.28.0
    "@babel/plugin-transform-parameters": ^7.27.7
    "@babel/traverse": ^7.28.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7c32c988b4b040d0091d0210b6b946249571858b2f33f3a5105f41c28ee0b8440a9dfb2aa46f3ae0d3014f86ddf16aee9a0cbf4229daf8e013235352b8f31fc9
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-object-super@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-replace-supers": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 46b819cb9a6cd3cfefe42d07875fee414f18d5e66040366ae856116db560ad4e16f3899a0a7fddd6773e0d1458444f94b208b67c0e3b6977a27ea17a5c13dbf6
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f4356b04cf21a98480f9788ea50f1f13ee88e89bb6393ba4b84d1f39a4a84c7928c9a4328e8f4c5b6deb218da68a8fd17bf4f46faec7653ddc20ffaaa5ba49f4
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-skip-transparent-expression-wrappers": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c4428d31f182d724db6f10575669aad3dbccceb0dea26aa9071fa89f11b3456278da3097fcc78937639a13c105a82cd452dc0218ce51abdbcf7626a013b928a5
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.27.7":
  version: 7.27.7
  resolution: "@babel/plugin-transform-parameters@npm:7.27.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d51f195e1d6ac5d9fce583e9a70a5bfe403e62386e5eb06db9fbc6533f895a98ff7e7c3dcaa311a8e6fa7a9794466e81cdabcba6af9f59d787fb767bfe7868b4
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-methods@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c76f8f6056946466116e67eb9d8014a2d748ade2062636ab82045c1dac9c233aff10e597777bc5af6f26428beb845ceb41b95007abef7d0484da95789da56662
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.1
    "@babel/helper-create-class-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: af539af1bd423aa46b9da83d649be716494ca80783841f47094b6741fa24e11141446027fd152ddff791dede9d4a76d0d5eb467402a2e584d7f5ea90e2673c7e
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-property-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7caec27d5ed8870895c9faf4f71def72745d69da0d8e77903146a4e135fd7bed5778f5f9cebb36c5fba86338e6194dd67a08c033fc84b4299b7eceab6d9630cb
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-constant-elements@npm:^7.21.3":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-constant-elements@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8372cf17ed551cd2e3da4f32a211881265692a17ad4c4fd40a8adcb89316d147db54f023630022ad7ec7474c4108647f67e3a62db43e515246a7574dcb5eeefe
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-display-name@npm:^7.27.1":
  version: 7.28.0
  resolution: "@babel/plugin-transform-react-display-name@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 268b1a9192974439d17949e170b01cac2a2aa003c844e2fe3b8361146f42f66487178cffdfa8ce862aa9e6c814bc37f879a70300cb3f067815d15fa6aad04e6d
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-development@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-development@npm:7.27.1"
  dependencies:
    "@babel/plugin-transform-react-jsx": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b88865d5b8c018992f2332da939faa15c4d4a864c9435a5937beaff3fe43781432cc42e0a5d5631098e0bd4066fc33f5fa72203b388b074c3545fe7aaa21e474
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.1
    "@babel/helper-module-imports": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/plugin-syntax-jsx": ^7.27.1
    "@babel/types": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 960d36e5d11ba68e4fbf1e2b935c153cb6ea7b0004f838aaee8baf7de30462b8f0562743a39ce3c370cc70b8f79d3c549104a415a615b2b0055b71fd025df0f3
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-pure-annotations@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-pure-annotations@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a6f591c5e85a1ab0685d4a25afe591fe8d11dc0b73c677cf9560ff8d540d036a1cce9efcb729fc9092def4d854dc304ffdc063a89a9247900b69c516bf971a4c
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/plugin-transform-regenerator@npm:7.28.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 40c76d47333caee8e30dbed9c54c56366f9caa2b5946070ea87317843ccd3ffb0b4008b64b667b08feca917e07f235af2af1ab9de097eaea9af60bb01a8cd5dd
  languageName: node
  linkType: hard

"@babel/plugin-transform-regexp-modifiers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-regexp-modifiers@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: f6cb385fe0e798bff7e9b20cf5912bf40e180895ff3610b1ccdce260f3c20daaebb3a99dc087c8168a99151cd3e16b94f4689fd5a4b01cf1834b45c133e620b2
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-reserved-words@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: dea0b66742d2863b369c06c053e11e15ba785892ea19cccf7aef3c1bdaa38b6ab082e19984c5ea7810d275d9445c5400fcc385ad71ce707ed9256fadb102af3b
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fbba6e2aef0b69681acb68202aa249c0598e470cc0853d7ff5bd0171fd6a7ec31d77cfabcce9df6360fc8349eded7e4a65218c32551bd3fc0caaa1ac899ac6d4
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-spread@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-skip-transparent-expression-wrappers": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 58b08085ee9c29955ac3b68d61c1a79728d44d19a69cb5eb669794aeaf54c57c6647af7b979c1297e81ede3d08b3ddcb1936ef39a533f28ff3e399a9be54dab1
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e1414a502efba92c7974681767e365a8cda6c5e9e5f33472a9eaa0ce2e75cea0a9bef881ff8dda37c7810ad902f98d3c00ead92a3ac3b73a79d011df85b5a189
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-template-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 93aad782503b691faef7c0893372d5243df3219b07f1f22cfc32c104af6a2e7acd6102c128439eab15336d048f1b214ca134b87b0630d8cd568bf447f78b25ce
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ed8048c8de72c60969a64cf2273cc6d9275d8fa8db9bd25a1268273a00fb9cbd79931140311411bda1443aa56cb3961fb911d1795abacde7f0482f1d8fdf0356
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.27.1":
  version: 7.28.0
  resolution: "@babel/plugin-transform-typescript@npm:7.28.0"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.27.3
    "@babel/helper-create-class-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-skip-transparent-expression-wrappers": ^7.27.1
    "@babel/plugin-syntax-typescript": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 14c1024bcd57fcd469d90cf0c15c3cd4e771e2eb2cd9afee3aa79b59c8ed103654f7c5c71cdb3bfe31c1d0cb08bfad8c80f5aa1d24b4b454bd21301d5925533d
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d817154bc10758ddd85b716e0bc1af1a1091e088400289ab6b78a1a4d609907ce3d2f1fd51a6fd0e0c8ecbb5f8e3aab4957e0747776d132d2379e85c3ef0520a
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-property-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-property-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5d99c89537d1ebaac3f526c04b162cf95a47d363d4829f78c6701a2c06ab78a48da66a94f853f85f44a3d72153410ba923e072bed4b7166fa097f503eb14131d
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a34d89a2b75fb78e66d97c3dc90d4877f7e31f43316b52176f95a5dee20e9bb56ecf158eafc42a001676ddf7b393d9e67650bad6b32f5405780f25fb83cd68e3
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-sets-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-sets-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.27.1
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 295126074c7388ab05c82ef3ed0907a1ee4666bbdd763477ead9aba6eb2c74bdf65669416861ac93d337a4a27640963bb214acadc2697275ce95aab14868d57f
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.20.2":
  version: 7.28.0
  resolution: "@babel/preset-env@npm:7.28.0"
  dependencies:
    "@babel/compat-data": ^7.28.0
    "@babel/helper-compilation-targets": ^7.27.2
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-validator-option": ^7.27.1
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key": ^7.27.1
    "@babel/plugin-bugfix-safari-class-field-initializer-scope": ^7.27.1
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": ^7.27.1
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": ^7.27.1
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": ^7.27.1
    "@babel/plugin-proposal-private-property-in-object": 7.21.0-placeholder-for-preset-env.2
    "@babel/plugin-syntax-import-assertions": ^7.27.1
    "@babel/plugin-syntax-import-attributes": ^7.27.1
    "@babel/plugin-syntax-unicode-sets-regex": ^7.18.6
    "@babel/plugin-transform-arrow-functions": ^7.27.1
    "@babel/plugin-transform-async-generator-functions": ^7.28.0
    "@babel/plugin-transform-async-to-generator": ^7.27.1
    "@babel/plugin-transform-block-scoped-functions": ^7.27.1
    "@babel/plugin-transform-block-scoping": ^7.28.0
    "@babel/plugin-transform-class-properties": ^7.27.1
    "@babel/plugin-transform-class-static-block": ^7.27.1
    "@babel/plugin-transform-classes": ^7.28.0
    "@babel/plugin-transform-computed-properties": ^7.27.1
    "@babel/plugin-transform-destructuring": ^7.28.0
    "@babel/plugin-transform-dotall-regex": ^7.27.1
    "@babel/plugin-transform-duplicate-keys": ^7.27.1
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex": ^7.27.1
    "@babel/plugin-transform-dynamic-import": ^7.27.1
    "@babel/plugin-transform-explicit-resource-management": ^7.28.0
    "@babel/plugin-transform-exponentiation-operator": ^7.27.1
    "@babel/plugin-transform-export-namespace-from": ^7.27.1
    "@babel/plugin-transform-for-of": ^7.27.1
    "@babel/plugin-transform-function-name": ^7.27.1
    "@babel/plugin-transform-json-strings": ^7.27.1
    "@babel/plugin-transform-literals": ^7.27.1
    "@babel/plugin-transform-logical-assignment-operators": ^7.27.1
    "@babel/plugin-transform-member-expression-literals": ^7.27.1
    "@babel/plugin-transform-modules-amd": ^7.27.1
    "@babel/plugin-transform-modules-commonjs": ^7.27.1
    "@babel/plugin-transform-modules-systemjs": ^7.27.1
    "@babel/plugin-transform-modules-umd": ^7.27.1
    "@babel/plugin-transform-named-capturing-groups-regex": ^7.27.1
    "@babel/plugin-transform-new-target": ^7.27.1
    "@babel/plugin-transform-nullish-coalescing-operator": ^7.27.1
    "@babel/plugin-transform-numeric-separator": ^7.27.1
    "@babel/plugin-transform-object-rest-spread": ^7.28.0
    "@babel/plugin-transform-object-super": ^7.27.1
    "@babel/plugin-transform-optional-catch-binding": ^7.27.1
    "@babel/plugin-transform-optional-chaining": ^7.27.1
    "@babel/plugin-transform-parameters": ^7.27.7
    "@babel/plugin-transform-private-methods": ^7.27.1
    "@babel/plugin-transform-private-property-in-object": ^7.27.1
    "@babel/plugin-transform-property-literals": ^7.27.1
    "@babel/plugin-transform-regenerator": ^7.28.0
    "@babel/plugin-transform-regexp-modifiers": ^7.27.1
    "@babel/plugin-transform-reserved-words": ^7.27.1
    "@babel/plugin-transform-shorthand-properties": ^7.27.1
    "@babel/plugin-transform-spread": ^7.27.1
    "@babel/plugin-transform-sticky-regex": ^7.27.1
    "@babel/plugin-transform-template-literals": ^7.27.1
    "@babel/plugin-transform-typeof-symbol": ^7.27.1
    "@babel/plugin-transform-unicode-escapes": ^7.27.1
    "@babel/plugin-transform-unicode-property-regex": ^7.27.1
    "@babel/plugin-transform-unicode-regex": ^7.27.1
    "@babel/plugin-transform-unicode-sets-regex": ^7.27.1
    "@babel/preset-modules": 0.1.6-no-external-plugins
    babel-plugin-polyfill-corejs2: ^0.4.14
    babel-plugin-polyfill-corejs3: ^0.13.0
    babel-plugin-polyfill-regenerator: ^0.6.5
    core-js-compat: ^3.43.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 90399ed6350ac413fb507dc5c9e29e98d10684c4b7c7c6ae7b204bb91a7a9cd3bf8f944167a931a73112c8c820d0d1f42d4c15d7c4a7cf19196bf11c19663513
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:0.1.6-no-external-plugins":
  version: 0.1.6-no-external-plugins
  resolution: "@babel/preset-modules@npm:0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils": ^7.0.0
    "@babel/types": ^7.4.4
    esutils: ^2.0.2
  peerDependencies:
    "@babel/core": ^7.0.0-0 || ^8.0.0-0 <8.0.0
  checksum: 4855e799bc50f2449fb5210f78ea9e8fd46cf4f242243f1e2ed838e2bd702e25e73e822e7f8447722a5f4baa5e67a8f7a0e403f3e7ce04540ff743a9c411c375
  languageName: node
  linkType: hard

"@babel/preset-react@npm:^7.18.6":
  version: 7.27.1
  resolution: "@babel/preset-react@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-validator-option": ^7.27.1
    "@babel/plugin-transform-react-display-name": ^7.27.1
    "@babel/plugin-transform-react-jsx": ^7.27.1
    "@babel/plugin-transform-react-jsx-development": ^7.27.1
    "@babel/plugin-transform-react-pure-annotations": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 00bc146f9c742eed804c598d3f31b7d889c1baf8c768989b7f84a93ca527dd1518d3b86781e89ca45cae6dbee136510d3a121658e01416c5578aecf751517bb5
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.21.0":
  version: 7.27.1
  resolution: "@babel/preset-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
    "@babel/helper-validator-option": ^7.27.1
    "@babel/plugin-syntax-jsx": ^7.27.1
    "@babel/plugin-transform-modules-commonjs": ^7.27.1
    "@babel/plugin-transform-typescript": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 38020f1b23e88ec4fbffd5737da455d8939244bddfb48a2516aef93fb5947bd9163fb807ce6eff3e43fa5ffe9113aa131305fef0fb5053998410bbfcfe6ce0ec
  languageName: node
  linkType: hard

"@babel/template@npm:^7.27.1, @babel/template@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": ^7.27.1
    "@babel/parser": ^7.27.2
    "@babel/types": ^7.27.1
  checksum: ff5628bc066060624afd970616090e5bba91c6240c2e4b458d13267a523572cbfcbf549391eec8217b94b064cf96571c6273f0c04b28a8567b96edc675c28e27
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.27.1, @babel/traverse@npm:^7.27.3, @babel/traverse@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/traverse@npm:7.28.0"
  dependencies:
    "@babel/code-frame": ^7.27.1
    "@babel/generator": ^7.28.0
    "@babel/helper-globals": ^7.28.0
    "@babel/parser": ^7.28.0
    "@babel/template": ^7.27.2
    "@babel/types": ^7.28.0
    debug: ^4.3.1
  checksum: f1b6ed2a37f593ee02db82521f8d54c8540a7ec2735c6c127ba687de306d62ac5a7c6471819783128e0b825c4f7e374206ebbd1daf00d07f05a4528f5b1b4c07
  languageName: node
  linkType: hard

"@babel/types@npm:^7.21.3, @babel/types@npm:^7.27.1, @babel/types@npm:^7.27.3, @babel/types@npm:^7.27.6, @babel/types@npm:^7.28.0, @babel/types@npm:^7.4.4":
  version: 7.28.0
  resolution: "@babel/types@npm:7.28.0"
  dependencies:
    "@babel/helper-string-parser": ^7.27.1
    "@babel/helper-validator-identifier": ^7.27.1
  checksum: 3cb33bbe79e9629c3e4ed1592340f936481e7aef2c3df11f8b1f91e54b45e89b3ad92f2d20f8acdb5a7e00157174ffe8b1d174069bb839303e7f39f579d60969
  languageName: node
  linkType: hard

"@emnapi/core@npm:^1.4.3":
  version: 1.4.3
  resolution: "@emnapi/core@npm:1.4.3"
  dependencies:
    "@emnapi/wasi-threads": 1.0.2
    tslib: ^2.4.0
  checksum: 1c757d380b3cecec637a2eccfb31b770b995060f695d1e15b29a86e2038909a24152947ef6e4b6586759e6716148ff17f40e51367d1b79c9a3e1b6812537bdf4
  languageName: node
  linkType: hard

"@emnapi/runtime@npm:^1.2.0":
  version: 1.4.4
  resolution: "@emnapi/runtime@npm:1.4.4"
  dependencies:
    tslib: ^2.4.0
  checksum: 49490b2630d258401af9d2fc6cfc4d302fe92e1557761380a9ce495a2d78aea67fb4dcb3f523eee396b24896548bce2b9d9e4cea01b62730cf527a47a52189da
  languageName: node
  linkType: hard

"@emnapi/runtime@npm:^1.4.3":
  version: 1.4.3
  resolution: "@emnapi/runtime@npm:1.4.3"
  dependencies:
    tslib: ^2.4.0
  checksum: ff2074809638ed878e476ece370c6eae7e6257bf029a581bb7a290488d8f2a08c420a65988c7f03bfc6bb689218f0cd995d2f935bd182150b357fc2341142f4f
  languageName: node
  linkType: hard

"@emnapi/wasi-threads@npm:1.0.2":
  version: 1.0.2
  resolution: "@emnapi/wasi-threads@npm:1.0.2"
  dependencies:
    tslib: ^2.4.0
  checksum: c289cd3d0e26f11de23429a4abc7f99927917c0871d5a22637cbb75170f2b58d3a42e80d76dea89d054e529f79e35cdc953324819a7f990305d0db2897fa5fab
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.7.0":
  version: 4.7.0
  resolution: "@eslint-community/eslint-utils@npm:4.7.0"
  dependencies:
    eslint-visitor-keys: ^3.4.3
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: b177e3b75c0b8d0e5d71f1c532edb7e40b31313db61f0c879f9bf19c3abb2783c6c372b5deb2396dab4432f2946b9972122ac682e77010376c029dfd0149c681
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.12.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 0d628680e204bc316d545b4993d3658427ca404ae646ce541fcc65306b8c712c340e5e573e30fb9f85f4855c0c5f6dca9868931f2fcced06417fbe1a0c6cd2d6
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.21.0":
  version: 0.21.0
  resolution: "@eslint/config-array@npm:0.21.0"
  dependencies:
    "@eslint/object-schema": ^2.1.6
    debug: ^4.3.1
    minimatch: ^3.1.2
  checksum: 84d3ae7cb755af94dc158a74389f4c560757b13f2bb908f598f927b87b70a38e8152015ea2e9557c1b4afc5130ee1356f6cad682050d67aae0468bbef98bc3a8
  languageName: node
  linkType: hard

"@eslint/config-helpers@npm:^0.3.0":
  version: 0.3.0
  resolution: "@eslint/config-helpers@npm:0.3.0"
  checksum: d4fe8242ef580806ddaa88309f4bb2d3e6be5524cc6d6197675106c6d048f766a3f9cdc2e8e33bbc97a123065792cac8314fc85ac2b3cf72610e8df59301d63a
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.14.0":
  version: 0.14.0
  resolution: "@eslint/core@npm:0.14.0"
  dependencies:
    "@types/json-schema": ^7.0.15
  checksum: d68b8282b6f38c5145234f812f18f491d12d716240875591bd54bf5ac32858d979bdf6d38e521997a6e01f2c4223a3e66049714151da7278d0a95ff15b5d46c8
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.15.1":
  version: 0.15.1
  resolution: "@eslint/core@npm:0.15.1"
  dependencies:
    "@types/json-schema": ^7.0.15
  checksum: 9215f00466d60764453466604443a491b0ea8263c148836fef723354d6ef1d550991e931d3df2780c99cee2cab14c4f41f97d5341ab12a8443236c961bb6f664
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3, @eslint/eslintrc@npm:^3.3.1":
  version: 3.3.1
  resolution: "@eslint/eslintrc@npm:3.3.1"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.3.2
    espree: ^10.0.1
    globals: ^14.0.0
    ignore: ^5.2.0
    import-fresh: ^3.2.1
    js-yaml: ^4.1.0
    minimatch: ^3.1.2
    strip-json-comments: ^3.1.1
  checksum: 8241f998f0857abf5a615072273b90b1244d75c1c45d217c6a8eb444c6e12bbb5506b4879c14fb262eb72b7d8e3d2f0542da2db1a7f414a12496ebb790fb4d62
  languageName: node
  linkType: hard

"@eslint/js@npm:9.30.1, @eslint/js@npm:^9.26.0":
  version: 9.30.1
  resolution: "@eslint/js@npm:9.30.1"
  checksum: 596adcd4336f098121b4f3f336169dabe86ca8d34b9fb4e30c9c44ccbb10def931bdbbd92cd910776c4030a05ae614fbc89fc8d09f69f5bad2795cd7157678e8
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.6":
  version: 2.1.6
  resolution: "@eslint/object-schema@npm:2.1.6"
  checksum: e32e565319f6544d36d3fa69a3e163120722d12d666d1a4525c9a6f02e9b54c29d9b1f03139e25d7e759e08dda8da433590bc23c09db8d511162157ef1b86a4c
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.3.1":
  version: 0.3.3
  resolution: "@eslint/plugin-kit@npm:0.3.3"
  dependencies:
    "@eslint/core": ^0.15.1
    levn: ^0.4.1
  checksum: c9dc7b83ed011dce35ccc66dc53aaaa87e9fb2bd7c8a11231f7624334d82c9a53552e4b1a1cb60b74073fcc49a2661be874e503aae14cf2f6ac6b1c7faeb7080
  languageName: node
  linkType: hard

"@floating-ui/core@npm:^1.7.2":
  version: 1.7.2
  resolution: "@floating-ui/core@npm:1.7.2"
  dependencies:
    "@floating-ui/utils": ^0.2.10
  checksum: aea540ea0101daf83e5beb2769af81f0532dcb8514dbee9d4c0a06576377d56dbfd4e5c3b031359594a26649734d1145bbd5524e8a573a745a5fcadc6b307906
  languageName: node
  linkType: hard

"@floating-ui/dom@npm:^1.7.2":
  version: 1.7.2
  resolution: "@floating-ui/dom@npm:1.7.2"
  dependencies:
    "@floating-ui/core": ^1.7.2
    "@floating-ui/utils": ^0.2.10
  checksum: 232d6668693cfecec038f3fb1f5398eace340427a9108701b895136c18d303d8bdd6237dce224626abf56a5a4592db776fbd0d187aa0f72c729bfca14a474b65
  languageName: node
  linkType: hard

"@floating-ui/react-dom@npm:^2.0.0":
  version: 2.1.4
  resolution: "@floating-ui/react-dom@npm:2.1.4"
  dependencies:
    "@floating-ui/dom": ^1.7.2
  peerDependencies:
    react: ">=16.8.0"
    react-dom: ">=16.8.0"
  checksum: be2cc094b0c5cd7f6a06c6c58944e4f070e231bdc8d9f84fc8246eedf91e1545a8a9e6d8560664054de126aae6520da57a30b7d81433cb2625641a08eea8f029
  languageName: node
  linkType: hard

"@floating-ui/utils@npm:^0.2.10":
  version: 0.2.10
  resolution: "@floating-ui/utils@npm:0.2.10"
  checksum: ffc4c24a46a665cfd0337e9aaf7de8415b572f8a0f323af39175e4b575582aed13d172e7f049eedeece9eaf022bad019c140a2d192580451984ae529bdf1285c
  languageName: node
  linkType: hard

"@hookform/resolvers@npm:^5.1.1":
  version: 5.1.1
  resolution: "@hookform/resolvers@npm:5.1.1"
  dependencies:
    "@standard-schema/utils": ^0.3.0
  peerDependencies:
    react-hook-form: ^7.55.0
  checksum: e6182147c400c7951dc3ac3eec7bf7518b5d6bbc90168b0d8b1190c30983ad31ad004cf7ae946af7ceb0339455eb699326edaef255d897819769e32ae99663d2
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 611e0545146f55ddfdd5c20239cfb7911f9d0e28258787c4fc1a1f6214250830c9367aaaeace0096ed90b6739bee1e9c52ad5ba8adaf74ab8b449119303babfe
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": ^0.19.1
    "@humanwhocodes/retry": ^0.3.0
  checksum: f9cb52bb235f8b9c6fcff43a7e500669a38f8d6ce26593404a9b56365a1644e0ed60c720dc65ff6a696b1f85f3563ab055bb554ec8674f2559085ba840e47710
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 0fd22007db8034a2cdf2c764b140d37d9020bbfce8a49d3ec5c05290e77d4b0263b1b972b752df8c89e5eaa94073408f2b7d977aed131faf6cf396ebb5d7fb61
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 7e5517bb51dbea3e02ab6cacef59a8f4b0ca023fc4b0b8cbc40de0ad29f46edd50b897c6e7fba79366a0217e3f48e2da8975056f6c35cfe19d9cc48f1d03c1dd
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.2":
  version: 0.4.3
  resolution: "@humanwhocodes/retry@npm:0.4.3"
  checksum: d423455b9d53cf01f778603404512a4246fb19b83e74fe3e28c70d9a80e9d4ae147d2411628907ca983e91a855a52535859a8bb218050bc3f6dbd7a553b7b442
  languageName: node
  linkType: hard

"@img/sharp-darwin-arm64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-darwin-arm64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-darwin-arm64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-darwin-arm64":
      optional: true
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-darwin-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-darwin-x64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-darwin-x64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-darwin-x64":
      optional: true
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-arm64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-darwin-arm64@npm:1.0.4"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-x64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-darwin-x64@npm:1.0.4"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linux-arm64@npm:1.0.4"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm@npm:1.0.5":
  version: 1.0.5
  resolution: "@img/sharp-libvips-linux-arm@npm:1.0.5"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-s390x@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linux-s390x@npm:1.0.4"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-x64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linux-x64@npm:1.0.4"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-arm64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linuxmusl-arm64@npm:1.0.4"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-x64@npm:1.0.4":
  version: 1.0.4
  resolution: "@img/sharp-libvips-linuxmusl-x64@npm:1.0.4"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-linux-arm64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-arm64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-arm64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm64":
      optional: true
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-arm@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-arm@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-arm": 1.0.5
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm":
      optional: true
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-s390x@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-s390x@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-s390x": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linux-s390x":
      optional: true
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linux-x64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linux-x64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linux-x64":
      optional: true
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-arm64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linuxmusl-arm64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linuxmusl-arm64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-linuxmusl-x64@npm:0.33.5"
  dependencies:
    "@img/sharp-libvips-linuxmusl-x64": 1.0.4
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-wasm32@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-wasm32@npm:0.33.5"
  dependencies:
    "@emnapi/runtime": ^1.2.0
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@img/sharp-win32-ia32@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-win32-ia32@npm:0.33.5"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@img/sharp-win32-x64@npm:0.33.5":
  version: 0.33.5
  resolution: "@img/sharp-win32-x64@npm:0.33.5"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: ^7.0.4
  checksum: 5d36d289960e886484362d9eb6a51d1ea28baed5f5d0140bbe62b99bac52eaf06cc01c2bc0d3575977962f84f6b2c4387b043ee632216643d4787b0999465bf2
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.12, @jridgewell/gen-mapping@npm:^0.3.2, @jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.12
  resolution: "@jridgewell/gen-mapping@npm:0.3.12"
  dependencies:
    "@jridgewell/sourcemap-codec": ^1.5.0
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: 56ee1631945084897f274e65348afbaca7970ce92e3c23b3a23b2fe5d0d2f0c67614f0df0f2bb070e585e944bbaaf0c11cee3a36318ab8a36af46f2fd566bc40
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 83b85f72c59d1c080b4cbec0fef84528963a1b5db34e4370fa4bd1e3ff64a0d80e0cee7369d11d73c704e0286fb2865b530acac7a871088fbe92b5edf1000870
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.10
  resolution: "@jridgewell/source-map@npm:0.3.10"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.25
  checksum: 035d6e6df0e60744506b14033f1569fd5ddc269abeb68bf50c2911118e2a4fa50dab474d49a59a993e4ee6795c4ae5940381e0d09fc204972c5387788d22d010
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.4
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.4"
  checksum: 959093724bfbc7c1c9aadc08066154f5c1f2acc647b45bd59beec46922cbfc6a9eda4a2114656de5bc00bb3600e420ea9a4cb05e68dcf388619f573b77bd9f0c
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25, @jridgewell/trace-mapping@npm:^0.3.28":
  version: 0.3.29
  resolution: "@jridgewell/trace-mapping@npm:0.3.29"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 5e92eeafa5131a4f6b7122063833d657f885cb581c812da54f705d7a599ff36a75a4a093a83b0f6c7e95642f5772dd94753f696915e8afea082237abf7423ca3
  languageName: node
  linkType: hard

"@medusajs/js-sdk@npm:^2.8.7":
  version: 2.8.7
  resolution: "@medusajs/js-sdk@npm:2.8.7"
  dependencies:
    "@medusajs/types": 2.8.7
    fetch-event-stream: ^0.1.5
    qs: ^6.12.1
  checksum: 5b583465b8c4ac30b1b45a52cc272f61d84437f32997e490124f2c9869bb9c6e13132882c5c435637a088c2b592a4bc36a5dd93ce6c9ece1285c59e07c21412f
  languageName: node
  linkType: hard

"@medusajs/types@npm:2.8.7, @medusajs/types@npm:^2.8.7":
  version: 2.8.7
  resolution: "@medusajs/types@npm:2.8.7"
  dependencies:
    bignumber.js: ^9.1.2
  peerDependencies:
    awilix: ^8.0.1
    ioredis: ^5.4.1
    vite: ^5 || ^6
  peerDependenciesMeta:
    ioredis:
      optional: true
    vite:
      optional: true
  checksum: 03d1ddf7cc4f542ccda1eafad7cf4d5cb77b2e0bce5fff974486031f442271e04bd5762bab36d3dcaefe3e6fe1d2c8793ae025ba5673bc9e65cf4a123544b0df
  languageName: node
  linkType: hard

"@napi-rs/wasm-runtime@npm:^0.2.11":
  version: 0.2.11
  resolution: "@napi-rs/wasm-runtime@npm:0.2.11"
  dependencies:
    "@emnapi/core": ^1.4.3
    "@emnapi/runtime": ^1.4.3
    "@tybys/wasm-util": ^0.9.0
  checksum: 7c614625784ab467cc7b36b4d7384854891469d0ddce8ca831d28b2abdf8cb3f014d8e8a181c98000719effb46950ab9134b245ab9a8044ad7a7da725b40f858
  languageName: node
  linkType: hard

"@next/env@npm:15.1.7":
  version: 15.1.7
  resolution: "@next/env@npm:15.1.7"
  checksum: 404c9065d4e488afa514c111f90a63e0e9945f65e2fffec835c58dd5d1381b8fed95db07629372f22e124f59b4f8b7e81151d647ec80038ef1c3e4b4ca8ad361
  languageName: node
  linkType: hard

"@next/eslint-plugin-next@npm:15.1.7":
  version: 15.1.7
  resolution: "@next/eslint-plugin-next@npm:15.1.7"
  dependencies:
    fast-glob: 3.3.1
  checksum: a0cb7fc8f94db9c24edbd6debcd7a47a0b07b9473406ef4d97d7b2856feb5680396a96486c34af17e427b38fc03e57092f0b183022914f86320697f136bae51b
  languageName: node
  linkType: hard

"@next/swc-darwin-arm64@npm:15.1.7":
  version: 15.1.7
  resolution: "@next/swc-darwin-arm64@npm:15.1.7"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-darwin-x64@npm:15.1.7":
  version: 15.1.7
  resolution: "@next/swc-darwin-x64@npm:15.1.7"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-gnu@npm:15.1.7":
  version: 15.1.7
  resolution: "@next/swc-linux-arm64-gnu@npm:15.1.7"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-musl@npm:15.1.7":
  version: 15.1.7
  resolution: "@next/swc-linux-arm64-musl@npm:15.1.7"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-linux-x64-gnu@npm:15.1.7":
  version: 15.1.7
  resolution: "@next/swc-linux-x64-gnu@npm:15.1.7"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-x64-musl@npm:15.1.7":
  version: 15.1.7
  resolution: "@next/swc-linux-x64-musl@npm:15.1.7"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-win32-arm64-msvc@npm:15.1.7":
  version: 15.1.7
  resolution: "@next/swc-win32-arm64-msvc@npm:15.1.7"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-win32-x64-msvc@npm:15.1.7":
  version: 15.1.7
  resolution: "@next/swc-win32-x64-msvc@npm:15.1.7"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@nolyfill/is-core-module@npm:1.0.39":
  version: 1.0.39
  resolution: "@nolyfill/is-core-module@npm:1.0.39"
  checksum: 0d6e098b871eca71d875651288e1f0fa770a63478b0b50479c99dc760c64175a56b5b04f58d5581bbcc6b552b8191ab415eada093d8df9597ab3423c8cac1815
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: ^7.1.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.1
    lru-cache: ^10.0.1
    socks-proxy-agent: ^8.0.3
  checksum: e8fc25d536250ed3e669813b36e8c6d805628b472353c57afd8c4fde0fcfcf3dda4ffe22f7af8c9070812ec2e7a03fb41d7151547cef3508efe661a5a3add20f
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: ^7.3.5
  checksum: 68951c589e9a4328698a35fd82fe71909a257d6f2ede0434d236fa55634f0fbcad9bb8755553ce5849bd25ee6f019f4d435921ac715c853582c4a7f5983c8d4a
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@pkgr/core@npm:^0.2.4":
  version: 0.2.7
  resolution: "@pkgr/core@npm:0.2.7"
  checksum: b16959878940f3d3016b79a4b2c23fd518aaec6b47295baa3154fbcf6574fee644c51023bb69069fa3ea9cdcaca40432818f54695f11acc0ae326cf56676e4d1
  languageName: node
  linkType: hard

"@radix-ui/number@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/number@npm:1.1.1"
  checksum: 58717faf3f7aa180fdfcde7083cae0bc06677cbd08fd2bed5a3f8820deeb6f514f7d475f1fbb61e1f9a16cb2e7daf1000b2c614b0de3520fccfc04e3576e4566
  languageName: node
  linkType: hard

"@radix-ui/primitive@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/primitive@npm:1.1.2"
  checksum: 6cb2ac097faf77b7288bdfd87d92e983e357252d00ee0d2b51ad8e7897bf9f51ec53eafd7dd64c613671a2b02cb8166177bc3de444a6560ec60835c363321c18
  languageName: node
  linkType: hard

"@radix-ui/react-accordion@npm:^1.2.11":
  version: 1.2.11
  resolution: "@radix-ui/react-accordion@npm:1.2.11"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-collapsible": 1.1.11
    "@radix-ui/react-collection": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-controllable-state": 1.2.2
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 1fd2780d58055b2e475873207157736d8b090cefba956f5dec0e6a42e2f8f09367e40ec8696a1509bbf47a526b448a6bb14e53978e38abe9875c24a95493ac28
  languageName: node
  linkType: hard

"@radix-ui/react-arrow@npm:1.1.7":
  version: 1.1.7
  resolution: "@radix-ui/react-arrow@npm:1.1.7"
  dependencies:
    "@radix-ui/react-primitive": 2.1.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 6cdf74f06090f8994cdf6d3935a44ea3ac309163a4f59c476482c4907e8e0775f224045030abf10fa4f9e1cb7743db034429249b9e59354988e247eeb0f4fdcf
  languageName: node
  linkType: hard

"@radix-ui/react-checkbox@npm:^1.3.2":
  version: 1.3.2
  resolution: "@radix-ui/react-checkbox@npm:1.3.2"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-controllable-state": 1.2.2
    "@radix-ui/react-use-previous": 1.1.1
    "@radix-ui/react-use-size": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 4c895aa1d9fa469d429a1a7ce39c10c8c056aba7e55acd9b257d1169f3826721d815f5d3e1543014f563aed379885d60aabcb23629a75cbbf18d6257e860c20a
  languageName: node
  linkType: hard

"@radix-ui/react-collapsible@npm:1.1.11":
  version: 1.1.11
  resolution: "@radix-ui/react-collapsible@npm:1.1.11"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-controllable-state": 1.2.2
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: a7ef7992caa3c02952dd8210493c1807739a8ac2ffb6ecf9b07974de65ed49a93b743e325e2dcd23318ea658328a01f750ecd3a965db68059a84b14988892dc4
  languageName: node
  linkType: hard

"@radix-ui/react-collection@npm:1.1.7":
  version: 1.1.7
  resolution: "@radix-ui/react-collection@npm:1.1.7"
  dependencies:
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-slot": 1.2.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: dd9bb015ef86205b4246f55bc84e5ad54519bb89b4825dd83e646fe95205191fe376bb31a9e847f9d66b710d0ef7fc9353c0b0ded7e8997a5c1f5be6addf94ef
  languageName: node
  linkType: hard

"@radix-ui/react-compose-refs@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-compose-refs@npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 9a91f0213014ffa40c5b8aae4debb993be5654217e504e35aa7422887eb2d114486d37e53c482d0fffb00cd44f51b5269fcdf397b280c71666fa11b7f32f165d
  languageName: node
  linkType: hard

"@radix-ui/react-context@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-context@npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 6d08437f23df362672259e535ae463e70bf7a0069f09bfa06c983a5a90e15250bde19da1d63ef8e3da06df1e1b4f92afa9d28ca6aa0297bb1c8aaf6ca83d28c5
  languageName: node
  linkType: hard

"@radix-ui/react-dialog@npm:^1.1.14":
  version: 1.1.14
  resolution: "@radix-ui/react-dialog@npm:1.1.14"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-dismissable-layer": 1.1.10
    "@radix-ui/react-focus-guards": 1.1.2
    "@radix-ui/react-focus-scope": 1.1.7
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-portal": 1.1.9
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-slot": 1.2.3
    "@radix-ui/react-use-controllable-state": 1.2.2
    aria-hidden: ^1.2.4
    react-remove-scroll: ^2.6.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 4928c0bf84b3a054eb3b4659b8e87192d8c120333d8437fcbd9d9311502d5eea9e9c87173929d4bfbc0db61b1134fcd98015756011d67ddcd2aed1b4a0134d7c
  languageName: node
  linkType: hard

"@radix-ui/react-direction@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-direction@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 8cc330285f1d06829568042ca9aabd3295be4690ae93683033fc8632b5c4dfc60f5c1312f6e2cae27c196189c719de3cfbcf792ff74800f9ccae0ab4abc1bc92
  languageName: node
  linkType: hard

"@radix-ui/react-dismissable-layer@npm:1.1.10":
  version: 1.1.10
  resolution: "@radix-ui/react-dismissable-layer@npm:1.1.10"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    "@radix-ui/react-use-escape-keydown": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: c4f31e8e93ae979a1bcd60726f8ebe7b79f23baafcd1d1e65f62cff6b322b2c6ff6132d82f2e63737f9955a8f04407849036f5b64b478e9a5678747d835957d8
  languageName: node
  linkType: hard

"@radix-ui/react-dropdown-menu@npm:^2.1.15":
  version: 2.1.15
  resolution: "@radix-ui/react-dropdown-menu@npm:2.1.15"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-menu": 2.1.15
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-controllable-state": 1.2.2
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: d95104c27eb3ddd0c03cd0736cd056f0cd1171ef49c4a9b331f2c3b4e67272bf0cf2ef2a03a5966a1ac79524a004cf27919c47b42788db63a42bb7585e0b306c
  languageName: node
  linkType: hard

"@radix-ui/react-focus-guards@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-focus-guards@npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 618658e2b98575198b94ccfdd27f41beb37f83721c9a04617e848afbc47461124ae008d703d713b9644771d96d4852e49de322cf4be3b5f10a4f94d200db5248
  languageName: node
  linkType: hard

"@radix-ui/react-focus-scope@npm:1.1.7":
  version: 1.1.7
  resolution: "@radix-ui/react-focus-scope@npm:1.1.7"
  dependencies:
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-callback-ref": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: bb642d192d3da8431f8b39f64959b493a7ba743af8501b76699ef93357c96507c11fb76d468824b52b0e024eaee130a641f3a213268ac7c9af34883b45610c9b
  languageName: node
  linkType: hard

"@radix-ui/react-id@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-id@npm:1.1.1"
  dependencies:
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 8d68e200778eb3038906870fc869b3d881f4a46715fb20cddd9c76cba42fdaaa4810a3365b6ec2daf0f185b9201fc99d009167f59c7921bc3a139722c2e976db
  languageName: node
  linkType: hard

"@radix-ui/react-menu@npm:2.1.15":
  version: 2.1.15
  resolution: "@radix-ui/react-menu@npm:2.1.15"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-collection": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-dismissable-layer": 1.1.10
    "@radix-ui/react-focus-guards": 1.1.2
    "@radix-ui/react-focus-scope": 1.1.7
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-popper": 1.2.7
    "@radix-ui/react-portal": 1.1.9
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-roving-focus": 1.1.10
    "@radix-ui/react-slot": 1.2.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    aria-hidden: ^1.2.4
    react-remove-scroll: ^2.6.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 2e454089f8384bc4448645f4228656cd9b34301c783ede567b3902b4cb7f65e6516c2248785e24e6398430bbbfe88b8fb1c36224d70dfc30a961d5738a33355a
  languageName: node
  linkType: hard

"@radix-ui/react-navigation-menu@npm:^1.2.13":
  version: 1.2.13
  resolution: "@radix-ui/react-navigation-menu@npm:1.2.13"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-collection": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-dismissable-layer": 1.1.10
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    "@radix-ui/react-use-controllable-state": 1.2.2
    "@radix-ui/react-use-layout-effect": 1.1.1
    "@radix-ui/react-use-previous": 1.1.1
    "@radix-ui/react-visually-hidden": 1.2.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 665fe4ea3b0cf1dd0891a361898618cc47a196ec2650f943c66a30ac7780c19f2bc5817fafe2e455af4c86d64735aa390bd10e4b6af26922c61aafe06300754f
  languageName: node
  linkType: hard

"@radix-ui/react-popper@npm:1.2.7":
  version: 1.2.7
  resolution: "@radix-ui/react-popper@npm:1.2.7"
  dependencies:
    "@floating-ui/react-dom": ^2.0.0
    "@radix-ui/react-arrow": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    "@radix-ui/react-use-layout-effect": 1.1.1
    "@radix-ui/react-use-rect": 1.1.1
    "@radix-ui/react-use-size": 1.1.1
    "@radix-ui/rect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 1d672b8b635846501212eb0cd15273c8acdd31e76e78d2b9ba29ce29730d5a2d3a61a8ed49bb689c94f67f45d1dffe0d49449e0810f08c4e112d8aef8430e76d
  languageName: node
  linkType: hard

"@radix-ui/react-portal@npm:1.1.9":
  version: 1.1.9
  resolution: "@radix-ui/react-portal@npm:1.1.9"
  dependencies:
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: bd6be39bf021d5c917e2474ecba411e2625171f7ef96862b9af04bbd68833bb3662a7f1fbdeb5a7a237111b10e811e76d2cd03e957dadd6e668ef16541bfbd68
  languageName: node
  linkType: hard

"@radix-ui/react-presence@npm:1.1.4":
  version: 1.1.4
  resolution: "@radix-ui/react-presence@npm:1.1.4"
  dependencies:
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: d3b0976368fccdfa07100c1f07ca434d0092d4132d1ed4a5c213802f7318d77fc1fd61d1b7038b87e82912688fafa97d8af000a6cca4027b09d92c5477f79dd0
  languageName: node
  linkType: hard

"@radix-ui/react-primitive@npm:2.1.3":
  version: 2.1.3
  resolution: "@radix-ui/react-primitive@npm:2.1.3"
  dependencies:
    "@radix-ui/react-slot": 1.2.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 01f82e4bad76b57767198762c905e5bcea04f4f52129749791e31adfcb1b36f6fdc89c73c40017d812b6e25e4ac925d837214bb280cfeaa5dc383457ce6940b0
  languageName: node
  linkType: hard

"@radix-ui/react-roving-focus@npm:1.1.10":
  version: 1.1.10
  resolution: "@radix-ui/react-roving-focus@npm:1.1.10"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-collection": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    "@radix-ui/react-use-controllable-state": 1.2.2
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 9f6afabc82e3a018cbb48f4fc94ce046f776f894df2d66c96f6b78bf864d59d71f749406e30c4600f6415a6f703c66fdbb87b2841cfa919f10d1f6602df2a5b6
  languageName: node
  linkType: hard

"@radix-ui/react-select@npm:^2.2.5":
  version: 2.2.5
  resolution: "@radix-ui/react-select@npm:2.2.5"
  dependencies:
    "@radix-ui/number": 1.1.1
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-collection": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-dismissable-layer": 1.1.10
    "@radix-ui/react-focus-guards": 1.1.2
    "@radix-ui/react-focus-scope": 1.1.7
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-popper": 1.2.7
    "@radix-ui/react-portal": 1.1.9
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-slot": 1.2.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    "@radix-ui/react-use-controllable-state": 1.2.2
    "@radix-ui/react-use-layout-effect": 1.1.1
    "@radix-ui/react-use-previous": 1.1.1
    "@radix-ui/react-visually-hidden": 1.2.3
    aria-hidden: ^1.2.4
    react-remove-scroll: ^2.6.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 9f5f024744833c2f8730ea1dc7b4d3a5647336fab7809966f29bdc62a8ea970aafc451cf96c7c5a9d125225724ec29697c0b31b395012fef0c1f85a5425ca451
  languageName: node
  linkType: hard

"@radix-ui/react-slot@npm:1.2.3, @radix-ui/react-slot@npm:^1.2.3":
  version: 1.2.3
  resolution: "@radix-ui/react-slot@npm:1.2.3"
  dependencies:
    "@radix-ui/react-compose-refs": 1.1.2
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 2731089e15477dd5eef98a5757c36113dd932d0c52ff05123cd89f05f0412e95e5b205229185d1cd705cda4a674a838479cce2b3b46ed903f82f5d23d9e3f3c2
  languageName: node
  linkType: hard

"@radix-ui/react-use-callback-ref@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-callback-ref@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: cde8c40f1d4e79e6e71470218163a746858304bad03758ac84dc1f94247a046478e8e397518350c8d6609c84b7e78565441d7505bb3ed573afce82cfdcd19faf
  languageName: node
  linkType: hard

"@radix-ui/react-use-controllable-state@npm:1.2.2":
  version: 1.2.2
  resolution: "@radix-ui/react-use-controllable-state@npm:1.2.2"
  dependencies:
    "@radix-ui/react-use-effect-event": 0.0.2
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: b438ee199d0630bf95eaafe8bf4bce219e73b371cfc8465f47548bfa4ee231f1134b5c6696b242890a01a0fd25fa34a7b172346bbfc5ee25cfb28b3881b1dc92
  languageName: node
  linkType: hard

"@radix-ui/react-use-effect-event@npm:0.0.2":
  version: 0.0.2
  resolution: "@radix-ui/react-use-effect-event@npm:0.0.2"
  dependencies:
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 5a1950a30a399ea7e4b98154da9f536737a610de80189b7aacd4f064a89a3cd0d2a48571d527435227252e72e872bdb544ff6ffcfbdd02de2efd011be4aaa902
  languageName: node
  linkType: hard

"@radix-ui/react-use-escape-keydown@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-escape-keydown@npm:1.1.1"
  dependencies:
    "@radix-ui/react-use-callback-ref": 1.1.1
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 0eb0756c2c55ddcde9ff01446ab01c085ab2bf799173e97db7ef5f85126f9e8600225570801a1f64740e6d14c39ffe8eed7c14d29737345a5797f4622ac96f6f
  languageName: node
  linkType: hard

"@radix-ui/react-use-layout-effect@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-layout-effect@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: bad2ba4f206e6255263582bedfb7868773c400836f9a1b423c0b464ffe4a17e13d3f306d1ce19cf7a19a492e9d0e49747464f2656451bb7c6a99f5a57bd34de2
  languageName: node
  linkType: hard

"@radix-ui/react-use-previous@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-previous@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: ea6ea13523a0561dda9b14b9d44e299484816a6762d7fb50b91b27b6aec89f78c85245b69d5a904750d43919dbb7ef6ce6d3823639346675aa3a5cb9de32d984
  languageName: node
  linkType: hard

"@radix-ui/react-use-rect@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-rect@npm:1.1.1"
  dependencies:
    "@radix-ui/rect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 116461bebc49472f7497e66a9bd413541181b3d00c5e0aaeef45d790dc1fbd7c8dcea80b169ea273306228b9a3c2b70067e902d1fd5004b3057e3bbe35b9d55d
  languageName: node
  linkType: hard

"@radix-ui/react-use-size@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-size@npm:1.1.1"
  dependencies:
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 64e61f65feb67ffc80e1fc4a8d5e32480fb6d68475e2640377e021178dead101568cba5f936c9c33e6c142c7cf2fb5d76ad7b23ef80e556ba142d56cf306147b
  languageName: node
  linkType: hard

"@radix-ui/react-visually-hidden@npm:1.2.3":
  version: 1.2.3
  resolution: "@radix-ui/react-visually-hidden@npm:1.2.3"
  dependencies:
    "@radix-ui/react-primitive": 2.1.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 42296bde1ddf4af4e7445e914c35d6bc8406d6ede49f0a959a553e75b3ed21da09fda80a81c48d8ec058ed8129ce7137499d02ee26f90f0d3eaa2417922d6509
  languageName: node
  linkType: hard

"@radix-ui/rect@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/rect@npm:1.1.1"
  checksum: c1c111edeab70b14a735bca43601de6468c792482864b766ac8940b43321492e5c0ae62f92b156cecdc9265ec3c680c32b3fa0c8a90b5e796923a9af13c5dc20
  languageName: node
  linkType: hard

"@rtsao/scc@npm:^1.1.0":
  version: 1.1.0
  resolution: "@rtsao/scc@npm:1.1.0"
  checksum: 17d04adf404e04c1e61391ed97bca5117d4c2767a76ae3e879390d6dec7b317fcae68afbf9e98badee075d0b64fa60f287729c4942021b4d19cd01db77385c01
  languageName: node
  linkType: hard

"@rushstack/eslint-patch@npm:^1.10.3":
  version: 1.12.0
  resolution: "@rushstack/eslint-patch@npm:1.12.0"
  checksum: 186788a93e2f141f622696091a593727fe7964d4925236a308e29754e29dcb182377f8d292ae954d227fb0574433863af055c0156593a40fd525e88b76e891ec
  languageName: node
  linkType: hard

"@standard-schema/utils@npm:^0.3.0":
  version: 0.3.0
  resolution: "@standard-schema/utils@npm:0.3.0"
  checksum: 7084f875d322792f2e0a5904009434c8374b9345b09ba89828b68fd56fa3c2b366d35bf340d9e8c72736ef01793c2f70d350c372ed79845dc3566c58d34b4b51
  languageName: node
  linkType: hard

"@strapi/blocks-react-renderer@npm:^1.0.2":
  version: 1.0.2
  resolution: "@strapi/blocks-react-renderer@npm:1.0.2"
  peerDependencies:
    react: ^18.0.0 || ^19.0.0
    react-dom: ^18.0.0 || ^19.0.0
  checksum: 8f07e7ecce747e6d82a9ecfbda04d6ce8040ae3f215059875286dd4e15429ec58a9fde3f9c71c84d070d76ad64139cc268d3f75bdfd6db31a2bf1e99747daf33
  languageName: node
  linkType: hard

"@svgr/babel-plugin-add-jsx-attribute@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-add-jsx-attribute@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3fc8e35d16f5abe0af5efe5851f27581225ac405d6a1ca44cda0df064cddfcc29a428c48c2e4bef6cebf627c9ac2f652a096030edb02cf5a120ce28d3c234710
  languageName: node
  linkType: hard

"@svgr/babel-plugin-remove-jsx-attribute@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-remove-jsx-attribute@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ff992893c6c4ac802713ba3a97c13be34e62e6d981c813af40daabcd676df68a72a61bd1e692bb1eda3587f1b1d700ea462222ae2153bb0f46886632d4f88d08
  languageName: node
  linkType: hard

"@svgr/babel-plugin-remove-jsx-empty-expression@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-remove-jsx-empty-expression@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0fb691b63a21bac00da3aa2dccec50d0d5a5b347ff408d60803b84410d8af168f2656e4ba1ee1f24dab0ae4e4af77901f2928752bb0434c1f6788133ec599ec8
  languageName: node
  linkType: hard

"@svgr/babel-plugin-replace-jsx-attribute-value@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-replace-jsx-attribute-value@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1edda65ef4f4dd8f021143c8ec276a08f6baa6f733b8e8ee2e7775597bf6b97afb47fdeefd579d6ae6c959fe2e634f55cd61d99377631212228c8cfb351b8921
  languageName: node
  linkType: hard

"@svgr/babel-plugin-svg-dynamic-title@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-svg-dynamic-title@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 876cec891488992e6a9aebb8155e2bea4ec461b4718c51de36e988e00e271c6d9d01ef6be17b9effd44b2b3d7db0b41c161a5904a46ae6f38b26b387ad7f3709
  languageName: node
  linkType: hard

"@svgr/babel-plugin-svg-em-dimensions@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-svg-em-dimensions@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: be0e2d391164428327d9ec469a52cea7d93189c6b0e2c290999e048f597d777852f701c64dca44cd45b31ed14a7f859520326e2e4ad7c3a4545d0aa235bc7e9a
  languageName: node
  linkType: hard

"@svgr/babel-plugin-transform-react-native-svg@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/babel-plugin-transform-react-native-svg@npm:8.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 85b434a57572f53bd2b9f0606f253e1fcf57b4a8c554ec3f2d43ed17f50d8cae200cb3aaf1ec9d626e1456e8b135dce530ae047eb0bed6d4bf98a752d6640459
  languageName: node
  linkType: hard

"@svgr/babel-plugin-transform-svg-component@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-transform-svg-component@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 04e2023d75693eeb0890341c40e449881184663056c249be7e5c80168e4aabb0fadd255e8d5d2dbf54b8c2a6e700efba994377135bfa4060dc4a2e860116ef8c
  languageName: node
  linkType: hard

"@svgr/babel-preset@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/babel-preset@npm:8.1.0"
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute": 8.0.0
    "@svgr/babel-plugin-remove-jsx-attribute": 8.0.0
    "@svgr/babel-plugin-remove-jsx-empty-expression": 8.0.0
    "@svgr/babel-plugin-replace-jsx-attribute-value": 8.0.0
    "@svgr/babel-plugin-svg-dynamic-title": 8.0.0
    "@svgr/babel-plugin-svg-em-dimensions": 8.0.0
    "@svgr/babel-plugin-transform-react-native-svg": 8.1.0
    "@svgr/babel-plugin-transform-svg-component": 8.0.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3a67930f080b8891e1e8e2595716b879c944d253112bae763dce59807ba23454d162216c8d66a0a0e3d4f38a649ecd6c387e545d1e1261dd69a68e9a3392ee08
  languageName: node
  linkType: hard

"@svgr/core@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/core@npm:8.1.0"
  dependencies:
    "@babel/core": ^7.21.3
    "@svgr/babel-preset": 8.1.0
    camelcase: ^6.2.0
    cosmiconfig: ^8.1.3
    snake-case: ^3.0.4
  checksum: da4a12865c7dc59829d58df8bd232d6c85b7115fda40da0d2f844a1a51886e2e945560596ecfc0345d37837ac457de86a931e8b8d8550e729e0c688c02250d8a
  languageName: node
  linkType: hard

"@svgr/hast-util-to-babel-ast@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/hast-util-to-babel-ast@npm:8.0.0"
  dependencies:
    "@babel/types": ^7.21.3
    entities: ^4.4.0
  checksum: 88401281a38bbc7527e65ff5437970414391a86158ef4b4046c89764c156d2d39ecd7cce77be8a51994c9fb3249170cb1eb8b9128b62faaa81743ef6ed3534ab
  languageName: node
  linkType: hard

"@svgr/plugin-jsx@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/plugin-jsx@npm:8.1.0"
  dependencies:
    "@babel/core": ^7.21.3
    "@svgr/babel-preset": 8.1.0
    "@svgr/hast-util-to-babel-ast": 8.0.0
    svg-parser: ^2.0.4
  peerDependencies:
    "@svgr/core": "*"
  checksum: 0418a9780753d3544912ee2dad5d2cf8d12e1ba74df8053651b3886aeda54d5f0f7d2dece0af5e0d838332c4f139a57f0dabaa3ca1afa4d1a765efce6a7656f2
  languageName: node
  linkType: hard

"@svgr/plugin-svgo@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/plugin-svgo@npm:8.1.0"
  dependencies:
    cosmiconfig: ^8.1.3
    deepmerge: ^4.3.1
    svgo: ^3.0.2
  peerDependencies:
    "@svgr/core": "*"
  checksum: 59d9d214cebaacca9ca71a561f463d8b7e5a68ca9443e4792a42d903acd52259b1790c0680bc6afecc3f00a255a6cbd7ea278a9f625bac443620ea58a590c2d0
  languageName: node
  linkType: hard

"@svgr/webpack@npm:^8.1.0":
  version: 8.1.0
  resolution: "@svgr/webpack@npm:8.1.0"
  dependencies:
    "@babel/core": ^7.21.3
    "@babel/plugin-transform-react-constant-elements": ^7.21.3
    "@babel/preset-env": ^7.20.2
    "@babel/preset-react": ^7.18.6
    "@babel/preset-typescript": ^7.21.0
    "@svgr/core": 8.1.0
    "@svgr/plugin-jsx": 8.1.0
    "@svgr/plugin-svgo": 8.1.0
  checksum: c6eec5b0cf2fb2ecd3a7a362d272eda35330b17c76802a3481f499b5d07ff8f87b31d2571043bff399b051a1767b1e2e499dbf186104d1c06d76f9f1535fac01
  languageName: node
  linkType: hard

"@swc/counter@npm:0.1.3":
  version: 0.1.3
  resolution: "@swc/counter@npm:0.1.3"
  checksum: df8f9cfba9904d3d60f511664c70d23bb323b3a0803ec9890f60133954173047ba9bdeabce28cd70ba89ccd3fd6c71c7b0bd58be85f611e1ffbe5d5c18616598
  languageName: node
  linkType: hard

"@swc/helpers@npm:0.5.15":
  version: 0.5.15
  resolution: "@swc/helpers@npm:0.5.15"
  dependencies:
    tslib: ^2.8.0
  checksum: 1a9e0dbb792b2d1e0c914d69c201dbc96af3a0e6e6e8cf5a7f7d6a5d7b0e8b762915cd4447acb6b040e2ecc1ed49822875a7239f99a2d63c96c3c3407fb6fccf
  languageName: node
  linkType: hard

"@trysound/sax@npm:0.2.0":
  version: 0.2.0
  resolution: "@trysound/sax@npm:0.2.0"
  checksum: 11226c39b52b391719a2a92e10183e4260d9651f86edced166da1d95f39a0a1eaa470e44d14ac685ccd6d3df7e2002433782872c0feeb260d61e80f21250e65c
  languageName: node
  linkType: hard

"@tybys/wasm-util@npm:^0.9.0":
  version: 0.9.0
  resolution: "@tybys/wasm-util@npm:0.9.0"
  dependencies:
    tslib: ^2.4.0
  checksum: 8d44c64e64e39c746e45b5dff7b534716f20e1f6e8fc206f8e4c8ac454ec0eb35b65646e446dd80745bc898db37a4eca549a936766d447c2158c9c43d44e7708
  languageName: node
  linkType: hard

"@types/eslint-scope@npm:^3.7.7":
  version: 3.7.7
  resolution: "@types/eslint-scope@npm:3.7.7"
  dependencies:
    "@types/eslint": "*"
    "@types/estree": "*"
  checksum: e2889a124aaab0b89af1bab5959847c5bec09809209255de0e63b9f54c629a94781daa04adb66bffcdd742f5e25a17614fb933965093c0eea64aacda4309380e
  languageName: node
  linkType: hard

"@types/eslint@npm:*":
  version: 9.6.1
  resolution: "@types/eslint@npm:9.6.1"
  dependencies:
    "@types/estree": "*"
    "@types/json-schema": "*"
  checksum: c286e79707ab604b577cf8ce51d9bbb9780e3d6a68b38a83febe13fa05b8012c92de17c28532fac2b03d3c460123f5055d603a579685325246ca1c86828223e0
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:^1.0.6":
  version: 1.0.8
  resolution: "@types/estree@npm:1.0.8"
  checksum: bd93e2e415b6f182ec4da1074e1f36c480f1d26add3e696d54fb30c09bc470897e41361c8fd957bf0985024f8fbf1e6e2aff977d79352ef7eb93a5c6dcff6c11
  languageName: node
  linkType: hard

"@types/json-schema@npm:*, @types/json-schema@npm:^7.0.15, @types/json-schema@npm:^7.0.9":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 97ed0cb44d4070aecea772b7b2e2ed971e10c81ec87dd4ecc160322ffa55ff330dace1793489540e3e318d90942064bb697cc0f8989391797792d919737b3b98
  languageName: node
  linkType: hard

"@types/json5@npm:^0.0.29":
  version: 0.0.29
  resolution: "@types/json5@npm:0.0.29"
  checksum: e60b153664572116dfea673c5bda7778dbff150498f44f998e34b5886d8afc47f16799280e4b6e241c0472aef1bc36add771c569c68fc5125fc2ae519a3eb9ac
  languageName: node
  linkType: hard

"@types/lodash@npm:^4.14.195":
  version: 4.17.20
  resolution: "@types/lodash@npm:4.17.20"
  checksum: dc7bb4653514dd91117a4c4cec2c37e2b5a163d7643445e4757d76a360fabe064422ec7a42dde7450c5e7e0e7e678d5e6eae6d2a919abcddf581d81e63e63839
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:^24.0.10":
  version: 24.0.10
  resolution: "@types/node@npm:24.0.10"
  dependencies:
    undici-types: ~7.8.0
  checksum: 04e4d11a711a683d6ed71f8db86b35035771ee9c1f110cb904ca67454d3478f19c71f1f0ccd0ba5350e5ce63bea70d08c569f798f6f5a5d2a1d05c038f1f729f
  languageName: node
  linkType: hard

"@types/react-dom@npm:^19":
  version: 19.1.6
  resolution: "@types/react-dom@npm:19.1.6"
  peerDependencies:
    "@types/react": ^19.0.0
  checksum: b5b20b7f0797f34c5a11915b74dcf8b3b7a9da9fea90279975ce6f150ca5d31bb069dbb0838638a5e9e168098aa4bb4a6f61d078efa1bbb55d7f0bdfe47bb142
  languageName: node
  linkType: hard

"@types/react@npm:^19":
  version: 19.1.8
  resolution: "@types/react@npm:19.1.8"
  dependencies:
    csstype: ^3.0.2
  checksum: 17e0c74d9c01214938fa805aaa8b97925bf3c5514e88fdf94bec42c0a6d4abbc63d4e30255db176f46fd7f0aa89f8085b9b2b2fa5abaffbbf7e5009386ada892
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:8.35.1, @typescript-eslint/eslint-plugin@npm:^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0, @typescript-eslint/eslint-plugin@npm:^8.32.0":
  version: 8.35.1
  resolution: "@typescript-eslint/eslint-plugin@npm:8.35.1"
  dependencies:
    "@eslint-community/regexpp": ^4.10.0
    "@typescript-eslint/scope-manager": 8.35.1
    "@typescript-eslint/type-utils": 8.35.1
    "@typescript-eslint/utils": 8.35.1
    "@typescript-eslint/visitor-keys": 8.35.1
    graphemer: ^1.4.0
    ignore: ^7.0.0
    natural-compare: ^1.4.0
    ts-api-utils: ^2.1.0
  peerDependencies:
    "@typescript-eslint/parser": ^8.35.1
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: a087c7f76492573056e7a3e3726887b31c3ac8979f8e432877d6147be8790796a3430214e016b669f0d10ad46b0ae9163327dff819222fd78ae67f2916be66d8
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:8.35.1, @typescript-eslint/parser@npm:^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0, @typescript-eslint/parser@npm:^8.32.0":
  version: 8.35.1
  resolution: "@typescript-eslint/parser@npm:8.35.1"
  dependencies:
    "@typescript-eslint/scope-manager": 8.35.1
    "@typescript-eslint/types": 8.35.1
    "@typescript-eslint/typescript-estree": 8.35.1
    "@typescript-eslint/visitor-keys": 8.35.1
    debug: ^4.3.4
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 599486c0c38adb9a71b421f881d4d08689b6caa5d70a4fd6108aa9cc94e1b4bd58916c7270f7cdbf3def4ef43638b9701a59ef9ace883120098a79f153427af9
  languageName: node
  linkType: hard

"@typescript-eslint/project-service@npm:8.35.1":
  version: 8.35.1
  resolution: "@typescript-eslint/project-service@npm:8.35.1"
  dependencies:
    "@typescript-eslint/tsconfig-utils": ^8.35.1
    "@typescript-eslint/types": ^8.35.1
    debug: ^4.3.4
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 3a1edae8d1bc30c1c603719ce9ba70c19c8eae12221c471a27912794b73890ba3ced0828a03cb7316655a600af435beb7b41385221defad9b8651620cfeefd3f
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.35.1":
  version: 8.35.1
  resolution: "@typescript-eslint/scope-manager@npm:8.35.1"
  dependencies:
    "@typescript-eslint/types": 8.35.1
    "@typescript-eslint/visitor-keys": 8.35.1
  checksum: 922d50d1394859a5878168e3ba5854ec5a171d7c78d1f2d4bb106540eceda0ef1a1cf5feec09d2bc9b9bfda68a09b640d507cc9856e6ae8a89dc4b22ef5f869c
  languageName: node
  linkType: hard

"@typescript-eslint/tsconfig-utils@npm:8.35.1, @typescript-eslint/tsconfig-utils@npm:^8.35.1":
  version: 8.35.1
  resolution: "@typescript-eslint/tsconfig-utils@npm:8.35.1"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 6b6176ec7dbfbe53539bce3e7554f062ff4d220aa5cb5793d52067fe6c196d749e77557dca66f5bf1ee23972e948d5c59461fa3e11da9e34a0a27d9fb7585f5a
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.35.1":
  version: 8.35.1
  resolution: "@typescript-eslint/type-utils@npm:8.35.1"
  dependencies:
    "@typescript-eslint/typescript-estree": 8.35.1
    "@typescript-eslint/utils": 8.35.1
    debug: ^4.3.4
    ts-api-utils: ^2.1.0
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 5d1a793bd780e90ba46e504d35117dfa6b550ebc59b1d002345a09747bdf565f828c0cc2d976181183a2e0a53a8682565a4376239958ff5976a0c6654b3f63ad
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.35.1, @typescript-eslint/types@npm:^8.35.1":
  version: 8.35.1
  resolution: "@typescript-eslint/types@npm:8.35.1"
  checksum: c4187947f0ddffca824f6d672fffba49abdacb6ef24c229e448f97197d49a4441868d80933cfb979c854cf72b22d290ac7f0894b93791490dc728dbd36659861
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.35.1":
  version: 8.35.1
  resolution: "@typescript-eslint/typescript-estree@npm:8.35.1"
  dependencies:
    "@typescript-eslint/project-service": 8.35.1
    "@typescript-eslint/tsconfig-utils": 8.35.1
    "@typescript-eslint/types": 8.35.1
    "@typescript-eslint/visitor-keys": 8.35.1
    debug: ^4.3.4
    fast-glob: ^3.3.2
    is-glob: ^4.0.3
    minimatch: ^9.0.4
    semver: ^7.6.0
    ts-api-utils: ^2.1.0
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: ffe4165b1d30140e477107a16fc4a5cda300fbbbae26985a02fe30fad5d8413582458a4e89905fd046a3c73c07d2f28febd9c5947bfc965abdf68ca7307f213f
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.35.1":
  version: 8.35.1
  resolution: "@typescript-eslint/utils@npm:8.35.1"
  dependencies:
    "@eslint-community/eslint-utils": ^4.7.0
    "@typescript-eslint/scope-manager": 8.35.1
    "@typescript-eslint/types": 8.35.1
    "@typescript-eslint/typescript-estree": 8.35.1
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: dfc6b479e1f5908f36e9a6cc344e1a2337a3bce16ed812b24476cc0bb579068d50b1d0f3a7ef61601e84b0b13f5342a583490f7dda8235c80af1ef5f4e5454ec
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.35.1":
  version: 8.35.1
  resolution: "@typescript-eslint/visitor-keys@npm:8.35.1"
  dependencies:
    "@typescript-eslint/types": 8.35.1
    eslint-visitor-keys: ^4.2.1
  checksum: 8d3246483f9dc22455024d44cd9e92c0ee54b612b886782ca8ccfac2b35601429460bfdf66ebc7decb2c7322031fcd64e033f8e55fe1913e89f3662870a55b84
  languageName: node
  linkType: hard

"@unrs/resolver-binding-android-arm-eabi@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-android-arm-eabi@npm:1.9.2"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-android-arm64@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-android-arm64@npm:1.9.2"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-darwin-arm64@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-darwin-arm64@npm:1.9.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-darwin-x64@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-darwin-x64@npm:1.9.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-freebsd-x64@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-freebsd-x64@npm:1.9.2"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm-gnueabihf@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-arm-gnueabihf@npm:1.9.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm-musleabihf@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-arm-musleabihf@npm:1.9.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm64-gnu@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-arm64-gnu@npm:1.9.2"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm64-musl@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-arm64-musl@npm:1.9.2"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-ppc64-gnu@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-ppc64-gnu@npm:1.9.2"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-riscv64-gnu@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-riscv64-gnu@npm:1.9.2"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-riscv64-musl@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-riscv64-musl@npm:1.9.2"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-s390x-gnu@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-s390x-gnu@npm:1.9.2"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-x64-gnu@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-x64-gnu@npm:1.9.2"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-x64-musl@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-x64-musl@npm:1.9.2"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-wasm32-wasi@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-wasm32-wasi@npm:1.9.2"
  dependencies:
    "@napi-rs/wasm-runtime": ^0.2.11
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-arm64-msvc@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-win32-arm64-msvc@npm:1.9.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-ia32-msvc@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-win32-ia32-msvc@npm:1.9.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-x64-msvc@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-win32-x64-msvc@npm:1.9.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.14.1, @webassemblyjs/ast@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/ast@npm:1.14.1"
  dependencies:
    "@webassemblyjs/helper-numbers": 1.13.2
    "@webassemblyjs/helper-wasm-bytecode": 1.13.2
  checksum: f9154ad9ea14f6f2374ebe918c221fd69a4d4514126a1acc6fa4966e8d27ab28cb550a5e6880032cf620e19640578658a7e5a55bd2aad1e3db4e9d598b8f2099
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.13.2"
  checksum: e866ec8433f4a70baa511df5e8f2ebcd6c24f4e2cc6274c7c5aabe2bcce3459ea4680e0f35d450e1f3602acf3913b6b8e4f15069c8cfd34ae8609fb9a7d01795
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-api-error@npm:1.13.2"
  checksum: 48b5df7fd3095bb252f59a139fe2cbd999a62ac9b488123e9a0da3906ad8a2f2da7b2eb21d328c01a90da987380928706395c2897d1f3ed9e2125b6d75a920d0
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/helper-buffer@npm:1.14.1"
  checksum: b611e981dfd6a797c3d8fc3a772de29a6e55033737c2c09c31bb66c613bdbb2d25f915df1dee62a602c6acc057ca71128432fa8c3e22a893e1219dc454f14ede
  languageName: node
  linkType: hard

"@webassemblyjs/helper-numbers@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-numbers@npm:1.13.2"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser": 1.13.2
    "@webassemblyjs/helper-api-error": 1.13.2
    "@xtuc/long": 4.2.2
  checksum: 49e2c9bf9b66997e480f6b44d80f895b3cde4de52ac135921d28e144565edca6903a519f627f4089b5509de1d7f9e5023f0e1a94ff78a36c9e2eb30e7c18ffd2
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.13.2"
  checksum: 8e059e1c1f0294f4fc3df8e4eaff3c5ef6e2e1358f34ebc118eaf5070ed59e56ed7fc92b28be734ebde17c8d662d5d27e06ade686c282445135da083ae11c128
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": 1.14.1
    "@webassemblyjs/helper-buffer": 1.14.1
    "@webassemblyjs/helper-wasm-bytecode": 1.13.2
    "@webassemblyjs/wasm-gen": 1.14.1
  checksum: 0a08d454a63192cd66abf91b6f060ac4b466cef341262246e9dcc828dd4c8536195dea9b46a1244b1eac65b59b8b502164a771a190052a92ff0a0a2ded0f8f53
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/ieee754@npm:1.13.2"
  dependencies:
    "@xtuc/ieee754": ^1.2.0
  checksum: d7e3520baa37a7309fa7db4d73d69fb869878853b1ebd4b168821bd03fcc4c0e1669c06231315b0039035d9a7a462e53de3ad982da4a426a4b0743b5888e8673
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/leb128@npm:1.13.2"
  dependencies:
    "@xtuc/long": 4.2.2
  checksum: 64083507f7cff477a6d71a9e325d95665cea78ec8df99ca7c050e1cfbe300fbcf0842ca3dcf3b4fa55028350135588a4f879398d3dd2b6a8de9913ce7faf5333
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/utf8@npm:1.13.2"
  checksum: 95ec6052f30eefa8d50c9b2a3394d08b17d53a4aa52821451d41d774c126fa8f39b988fbf5bff56da86852a87c16d676e576775a4071e5e5ccf020cc85a4b281
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-edit@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": 1.14.1
    "@webassemblyjs/helper-buffer": 1.14.1
    "@webassemblyjs/helper-wasm-bytecode": 1.13.2
    "@webassemblyjs/helper-wasm-section": 1.14.1
    "@webassemblyjs/wasm-gen": 1.14.1
    "@webassemblyjs/wasm-opt": 1.14.1
    "@webassemblyjs/wasm-parser": 1.14.1
    "@webassemblyjs/wast-printer": 1.14.1
  checksum: 9341c3146bb1b7863f03d6050c2a66990f20384ca137388047bbe1feffacb599e94fca7b7c18287d17e2449ffb4005fdc7f41f674a6975af9ad8522756f8ffff
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-gen@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": 1.14.1
    "@webassemblyjs/helper-wasm-bytecode": 1.13.2
    "@webassemblyjs/ieee754": 1.13.2
    "@webassemblyjs/leb128": 1.13.2
    "@webassemblyjs/utf8": 1.13.2
  checksum: 401b12bec7431c4fc29d9414bbe40d3c6dc5be04d25a116657c42329f5481f0129f3b5834c293f26f0e42681ceac9157bf078ce9bdb6a7f78037c650373f98b2
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-opt@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": 1.14.1
    "@webassemblyjs/helper-buffer": 1.14.1
    "@webassemblyjs/wasm-gen": 1.14.1
    "@webassemblyjs/wasm-parser": 1.14.1
  checksum: 60c697a9e9129d8d23573856df0791ba33cea4a3bc2339044cae73128c0983802e5e50a42157b990eeafe1237eb8e7653db6de5f02b54a0ae7b81b02dcdf2ae9
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.14.1, @webassemblyjs/wasm-parser@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-parser@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": 1.14.1
    "@webassemblyjs/helper-api-error": 1.13.2
    "@webassemblyjs/helper-wasm-bytecode": 1.13.2
    "@webassemblyjs/ieee754": 1.13.2
    "@webassemblyjs/leb128": 1.13.2
    "@webassemblyjs/utf8": 1.13.2
  checksum: 93f1fe2676da465b4e824419d9812a3d7218de4c3addd4e916c04bc86055fa134416c1b67e4b7cbde8d728c0dce2721d06cc0bfe7a7db7c093a0898009937405
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wast-printer@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": 1.14.1
    "@xtuc/long": 4.2.2
  checksum: 517881a0554debe6945de719d100b2d8883a2d24ddf47552cdeda866341e2bb153cd824a864bc7e2a61190a4b66b18f9899907e0074e9e820d2912ac0789ea60
  languageName: node
  linkType: hard

"@xtuc/ieee754@npm:^1.2.0":
  version: 1.2.0
  resolution: "@xtuc/ieee754@npm:1.2.0"
  checksum: ac56d4ca6e17790f1b1677f978c0c6808b1900a5b138885d3da21732f62e30e8f0d9120fcf8f6edfff5100ca902b46f8dd7c1e3f903728634523981e80e2885a
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.2":
  version: 4.2.2
  resolution: "@xtuc/long@npm:4.2.2"
  checksum: 8ed0d477ce3bc9c6fe2bf6a6a2cc316bb9c4127c5a7827bae947fa8ec34c7092395c5a283cc300c05b5fa01cbbfa1f938f410a7bf75db7c7846fea41949989ec
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: e70b209f5f408dd3a3bbd0eec4b10a2ffd64704a4a3821d0969d84928cc490a8eb60f85b78a95622c1841113edac10161c62e52f5e7d0027aa26786a8136e02e
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0, acorn@npm:^8.15.0":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: 309c6b49aedf1a2e34aaf266de06de04aab6eb097c02375c66fdeb0f64556a6a823540409914fb364d9a11bc30d79d485a2eba29af47992d3490e9886c4391c3
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 87bb7ee54f5ecf0ccbfcba0b07473885c43ecd76cb29a8db17d6137a19d9f9cd443a2a7c5fd8a3f24d58ad8145f9eb49116344a66b107e1aeab82cf2383f4753
  languageName: node
  linkType: hard

"ajv-formats@npm:^2.1.1":
  version: 2.1.1
  resolution: "ajv-formats@npm:2.1.1"
  dependencies:
    ajv: ^8.0.0
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: 4a287d937f1ebaad4683249a4c40c0fa3beed30d9ddc0adba04859026a622da0d317851316ea64b3680dc60f5c3c708105ddd5d5db8fe595d9d0207fd19f90b7
  languageName: node
  linkType: hard

"ajv-keywords@npm:^5.1.0":
  version: 5.1.0
  resolution: "ajv-keywords@npm:5.1.0"
  dependencies:
    fast-deep-equal: ^3.1.3
  peerDependencies:
    ajv: ^8.8.2
  checksum: c35193940b853119242c6757787f09ecf89a2c19bcd36d03ed1a615e710d19d450cb448bfda407b939aba54b002368c8bff30529cc50a0536a8e10bcce300421
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"ajv@npm:^8.0.0, ajv@npm:^8.9.0":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: ^3.1.3
    fast-uri: ^3.0.1
    json-schema-traverse: ^1.0.0
    require-from-string: ^2.0.2
  checksum: 1797bf242cfffbaf3b870d13565bd1716b73f214bb7ada9a497063aada210200da36e3ed40237285f3255acc4feeae91b1fb183625331bad27da95973f7253d9
  languageName: node
  linkType: hard

"ansi-escapes@npm:^7.0.0":
  version: 7.0.0
  resolution: "ansi-escapes@npm:7.0.0"
  dependencies:
    environment: ^1.0.0
  checksum: 19baa61e68d1998c03b3b8bd023653a6c2667f0ed6caa9a00780ffd6f0a14f4a6563c57a38b3c0aba71bd704cd49c4c8df41be60bd81c957409f91e9dd49051f
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.0.0, ansi-styles@npm:^6.1.0, ansi-styles@npm:^6.2.1":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"any-promise@npm:^1.0.0":
  version: 1.3.0
  resolution: "any-promise@npm:1.3.0"
  checksum: 0ee8a9bdbe882c90464d75d1f55cf027f5458650c4bd1f0467e65aec38ccccda07ca5844969ee77ed46d04e7dded3eaceb027e8d32f385688523fe305fa7e1de
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"arg@npm:^5.0.2":
  version: 5.0.2
  resolution: "arg@npm:5.0.2"
  checksum: 6c69ada1a9943d332d9e5382393e897c500908d91d5cb735a01120d5f71daf1b339b7b8980cbeaba8fd1afc68e658a739746179e4315a26e8a28951ff9930078
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 83644b56493e89a254bae05702abf3a1101b4fa4d0ca31df1c9985275a5a5bd47b3c27b7fa0b71098d41114d8ca000e6ed90cad764b306f8a503665e4d517ced
  languageName: node
  linkType: hard

"aria-hidden@npm:^1.2.4":
  version: 1.2.6
  resolution: "aria-hidden@npm:1.2.6"
  dependencies:
    tslib: ^2.0.0
  checksum: 56409c55c43ad917607f3f3aa67748dcf30a27e8bb5cb3c5d86b43e38babadd63cd77731a27bc8a8c4332c2291741ed92333bf7ca45f8b99ebc87b94a8070a6e
  languageName: node
  linkType: hard

"aria-query@npm:^5.3.2":
  version: 5.3.2
  resolution: "aria-query@npm:5.3.2"
  checksum: d971175c85c10df0f6d14adfe6f1292409196114ab3c62f238e208b53103686f46cc70695a4f775b73bc65f6a09b6a092fd963c4f3a5a7d690c8fc5094925717
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    is-array-buffer: ^3.0.5
  checksum: 0ae3786195c3211b423e5be8dd93357870e6fb66357d81da968c2c39ef43583ef6eece1f9cb1caccdae4806739c65dea832b44b8593414313cd76a89795fca63
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.6, array-includes@npm:^3.1.8, array-includes@npm:^3.1.9":
  version: 3.1.9
  resolution: "array-includes@npm:3.1.9"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    define-properties: ^1.2.1
    es-abstract: ^1.24.0
    es-object-atoms: ^1.1.1
    get-intrinsic: ^1.3.0
    is-string: ^1.1.1
    math-intrinsics: ^1.1.0
  checksum: b58dc526fe415252e50319eaf88336e06e75aa673e3b58d252414739a4612dbe56e7b613fdcc7c90561dc9cf9202bbe5ca029ccd8c08362746459475ae5a8f3e
  languageName: node
  linkType: hard

"array.prototype.findlast@npm:^1.2.5":
  version: 1.2.5
  resolution: "array.prototype.findlast@npm:1.2.5"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    es-shim-unscopables: ^1.0.2
  checksum: 83ce4ad95bae07f136d316f5a7c3a5b911ac3296c3476abe60225bc4a17938bf37541972fcc37dd5adbc99cbb9c928c70bbbfc1c1ce549d41a415144030bb446
  languageName: node
  linkType: hard

"array.prototype.findlastindex@npm:^1.2.6":
  version: 1.2.6
  resolution: "array.prototype.findlastindex@npm:1.2.6"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    define-properties: ^1.2.1
    es-abstract: ^1.23.9
    es-errors: ^1.3.0
    es-object-atoms: ^1.1.1
    es-shim-unscopables: ^1.1.0
  checksum: bd2665bd51f674d4e1588ce5d5848a8adb255f414070e8e652585598b801480516df2c6cef2c60b6ea1a9189140411c49157a3f112d52e9eabb4e9fc80936ea6
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.1, array.prototype.flat@npm:^1.3.3":
  version: 1.3.3
  resolution: "array.prototype.flat@npm:1.3.3"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-shim-unscopables: ^1.0.2
  checksum: 5d5a7829ab2bb271a8d30a1c91e6271cef0ec534593c0fe6d2fb9ebf8bb62c1e5326e2fddcbbcbbe5872ca04f5e6b54a1ecf092e0af704fb538da9b2bfd95b40
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.2, array.prototype.flatmap@npm:^1.3.3":
  version: 1.3.3
  resolution: "array.prototype.flatmap@npm:1.3.3"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-shim-unscopables: ^1.0.2
  checksum: 11b4de09b1cf008be6031bb507d997ad6f1892e57dc9153583de6ebca0f74ea403fffe0f203461d359de05048d609f3f480d9b46fed4099652d8b62cc972f284
  languageName: node
  linkType: hard

"array.prototype.tosorted@npm:^1.1.4":
  version: 1.1.4
  resolution: "array.prototype.tosorted@npm:1.1.4"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.3
    es-errors: ^1.3.0
    es-shim-unscopables: ^1.0.2
  checksum: e4142d6f556bcbb4f393c02e7dbaea9af8f620c040450c2be137c9cbbd1a17f216b9c688c5f2c08fbb038ab83f55993fa6efdd9a05881d84693c7bcb5422127a
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: ^1.0.1
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
    is-array-buffer: ^3.0.4
  checksum: b1d1fd20be4e972a3779b1569226f6740170dca10f07aa4421d42cefeec61391e79c557cda8e771f5baefe47d878178cd4438f60916ce831813c08132bced765
  languageName: node
  linkType: hard

"ast-types-flow@npm:^0.0.8":
  version: 0.0.8
  resolution: "ast-types-flow@npm:0.0.8"
  checksum: 0a64706609a179233aac23817837abab614f3548c252a2d3d79ea1e10c74aa28a0846e11f466cf72771b6ed8713abc094dcf8c40c3ec4207da163efa525a94a8
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 9102e246d1ed9b37ac36f57f0a6ca55226876553251a31fc80677e71471f463a54c872dc78d5d7f80740c8ba624395cccbe8b60f7b690c4418f487d8e9fd1106
  languageName: node
  linkType: hard

"autoprefixer@npm:^10.4.2":
  version: 10.4.21
  resolution: "autoprefixer@npm:10.4.21"
  dependencies:
    browserslist: ^4.24.4
    caniuse-lite: ^1.0.30001702
    fraction.js: ^4.3.7
    normalize-range: ^0.1.2
    picocolors: ^1.1.1
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.1.0
  bin:
    autoprefixer: bin/autoprefixer
  checksum: 11770ce635a0520e457eaf2ff89056cd57094796a9f5d6d9375513388a5a016cd947333dcfd213b822fdd8a0b43ce68ae4958e79c6f077c41d87444c8cca0235
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: ^1.0.0
  checksum: 1aa3ffbfe6578276996de660848b6e95669d9a95ad149e3dd0c0cda77db6ee1dbd9d1dd723b65b6d277b882dd0c4b91a654ae9d3cf9e1254b7e93e4908d78fd3
  languageName: node
  linkType: hard

"axe-core@npm:^4.10.0":
  version: 4.10.3
  resolution: "axe-core@npm:4.10.3"
  checksum: e89fa5bcad9216f2de29bbdf95d6211d8c5b1025cbdcf56b6695c18b2e9a1eebd0b997a0141334169f6f062fc68fd39a5b97f86348d9f5be05958eade5c1ec78
  languageName: node
  linkType: hard

"axobject-query@npm:^4.1.0":
  version: 4.1.0
  resolution: "axobject-query@npm:4.1.0"
  checksum: 7d1e87bf0aa7ae7a76cd39ab627b7c48fda3dc40181303d9adce4ba1d5b5ce73b5e5403ee6626ec8e91090448c887294d6144e24b6741a976f5be9347e3ae1df
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.14":
  version: 0.4.14
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.14"
  dependencies:
    "@babel/compat-data": ^7.27.7
    "@babel/helper-define-polyfill-provider": ^0.6.5
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: d654334c1b4390d08282416144b7b6f3d74d2cab44b2bfa2b6405c828882c82907b8b67698dce1be046c218d2d4fe5bf7fb6d01879938f3129dad969e8cfc44d
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.13.0":
  version: 0.13.0
  resolution: "babel-plugin-polyfill-corejs3@npm:0.13.0"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.6.5
    core-js-compat: ^3.43.0
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: cf526031acd97ff2124e7c10e15047e6eeb0620d029c687f1dca99916a8fe6cac0e634b84c913db6cb68b7a024f82492ba8fdcc2a6266e7b05bdac2cba0c2434
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.6.5":
  version: 0.6.5
  resolution: "babel-plugin-polyfill-regenerator@npm:0.6.5"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.6.5
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: ed1932fa9a31e0752fd10ebf48ab9513a654987cab1182890839523cb898559d24ae0578fdc475d9f995390420e64eeaa4b0427045b56949dace3c725bc66dbb
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"bignumber.js@npm:^9.1.2":
  version: 9.3.1
  resolution: "bignumber.js@npm:9.3.1"
  checksum: 6ab100271a23a75bb8b99a4b1a34a1a94967ac0b9a52a198147607bd91064e72c6f356380d7a09cd687bf50d81ad2ed1a0a8edfaa90369c9003ed8bb2440d7f0
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: bcad01494e8a9283abf18c1b967af65ee79b0c6a9e6fcfafebfe91dbe6e0fc7272bafb73389e198b310516ae04f7ad17d79aacf6cb4c0d5d5202a7e2e52c7d98
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.12
  resolution: "brace-expansion@npm:1.1.12"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: 12cb6d6310629e3048cadb003e1aca4d8c9bb5c67c3c321bafdd7e7a50155de081f78ea3e0ed92ecc75a9015e784f301efc8132383132f4f7904ad1ac529c562
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.2
  resolution: "brace-expansion@npm:2.0.2"
  dependencies:
    balanced-match: ^1.0.0
  checksum: 01dff195e3646bc4b0d27b63d9bab84d2ebc06121ff5013ad6e5356daa5a9d6b60fa26cf73c74797f2dc3fbec112af13578d51f75228c1112b26c790a87b0488
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: b95aa0b3bd909f6cd1720ffcf031aeaf46154dd88b4da01f9a1d3f7ea866a79eba76a6d01cbc3c422b2ee5cdc39a4f02491058d5df0d7bf6e6a162a832df1f69
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0, browserslist@npm:^4.24.4, browserslist@npm:^4.25.0":
  version: 4.25.1
  resolution: "browserslist@npm:4.25.1"
  dependencies:
    caniuse-lite: ^1.0.30001726
    electron-to-chromium: ^1.5.173
    node-releases: ^2.0.19
    update-browserslist-db: ^1.1.3
  bin:
    browserslist: cli.js
  checksum: 2a7e4317e809b09a436456221a1fcb8ccbd101bada187ed217f7a07a9e42ced822c7c86a0a4333d7d1b4e6e0c859d201732ffff1585d6bcacd8d226f6ddce7e3
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"busboy@npm:1.6.0":
  version: 1.6.0
  resolution: "busboy@npm:1.6.0"
  dependencies:
    streamsearch: ^1.1.0
  checksum: 32801e2c0164e12106bf236291a00795c3c4e4b709ae02132883fe8478ba2ae23743b11c5735a0aae8afe65ac4b6ca4568b91f0d9fed1fdbc32ede824a73746e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": ^4.0.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^10.0.1
    minipass: ^7.0.3
    minipass-collect: ^2.0.1
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^7.0.2
    ssri: ^12.0.0
    tar: ^7.4.3
    unique-filename: ^4.0.0
  checksum: e95684717de6881b4cdaa949fa7574e3171946421cd8291769dd3d2417dbf7abf4aa557d1f968cca83dcbc95bed2a281072b09abfc977c942413146ef7ed4525
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: ^1.3.0
    function-bind: ^1.1.2
  checksum: b2863d74fcf2a6948221f65d95b91b4b2d90cfe8927650b506141e669f7d5de65cea191bf788838bc40d13846b7886c5bc5c84ab96c3adbcf88ad69a72fcdc6b
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: ^1.0.0
    es-define-property: ^1.0.0
    get-intrinsic: ^1.2.4
    set-function-length: ^1.2.2
  checksum: aa2899bce917a5392fd73bd32e71799c37c0b7ab454e0ed13af7f6727549091182aade8bbb7b55f304a5bc436d543241c14090fb8a3137e9875e23f444f4f5a9
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: ^1.0.2
    get-intrinsic: ^1.3.0
  checksum: 2f6399488d1c272f56306ca60ff696575e2b7f31daf23bc11574798c84d9f2759dceb0cb1f471a85b77f28962a7ac6411f51d283ea2e45319009a19b6ccab3b2
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase-css@npm:^2.0.1":
  version: 2.0.1
  resolution: "camelcase-css@npm:2.0.1"
  checksum: 1cec2b3b3dcb5026688a470b00299a8db7d904c4802845c353dbd12d9d248d3346949a814d83bfd988d4d2e5b9904c07efe76fecd195a1d4f05b543e7c0b56b1
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001579, caniuse-lite@npm:^1.0.30001702, caniuse-lite@npm:^1.0.30001726":
  version: 1.0.30001726
  resolution: "caniuse-lite@npm:1.0.30001726"
  checksum: 6148fc2c2f46ae1faf74af932aa768031355e2394164d7ccba0caafd30310c9b4f2c723d19d5ae5fab05a3238d1bed7e98037c84199d29d40768168e848135f5
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"chalk@npm:^5.4.1":
  version: 5.4.1
  resolution: "chalk@npm:5.4.1"
  checksum: 0c656f30b782fed4d99198825c0860158901f449a6b12b818b0aabad27ec970389e7e8767d0e00762175b23620c812e70c4fd92c0210e55fc2d993638b74e86e
  languageName: node
  linkType: hard

"chokidar@npm:^3.6.0":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: d2f29f499705dcd4f6f3bbed79a9ce2388cf530460122eed3b9c48efeab7a4e28739c6551fd15bec9245c6b9eeca7a32baa64694d64d9b6faeb74ddb8c4a413d
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: fd73a4bab48b79e66903fe1cafbdc208956f41ea4f856df883d0c7277b7ab29fd33ee65f93b2ec9192fc0169238f2f8307b7735d27c155821d886b84aa97aa8d
  languageName: node
  linkType: hard

"chrome-trace-event@npm:^1.0.2":
  version: 1.0.4
  resolution: "chrome-trace-event@npm:1.0.4"
  checksum: fcbbd9dd0cd5b48444319007cc0c15870fd8612cc0df320908aa9d5e8a244084d48571eb28bf3c58c19327d2c5838f354c2d89fac3956d8e992273437401ac19
  languageName: node
  linkType: hard

"class-variance-authority@npm:^0.7.1":
  version: 0.7.1
  resolution: "class-variance-authority@npm:0.7.1"
  dependencies:
    clsx: ^2.1.1
  checksum: e05ba26ef9ec38f7c675047ce366b067d60af6c954dba08f7802af19a9460a534ae752d8fe1294fff99d0fa94a669b16ccebd87e8a20f637c0736cf2751dd2c5
  languageName: node
  linkType: hard

"cli-cursor@npm:^5.0.0":
  version: 5.0.0
  resolution: "cli-cursor@npm:5.0.0"
  dependencies:
    restore-cursor: ^5.0.0
  checksum: 1eb9a3f878b31addfe8d82c6d915ec2330cec8447ab1f117f4aa34f0137fbb3137ec3466e1c9a65bcb7557f6e486d343f2da57f253a2f668d691372dfa15c090
  languageName: node
  linkType: hard

"cli-truncate@npm:^4.0.0":
  version: 4.0.0
  resolution: "cli-truncate@npm:4.0.0"
  dependencies:
    slice-ansi: ^5.0.0
    string-width: ^7.0.0
  checksum: d5149175fd25ca985731bdeec46a55ec237475cf74c1a5e103baea696aceb45e372ac4acbaabf1316f06bd62e348123060f8191ffadfeedebd2a70a2a7fb199d
  languageName: node
  linkType: hard

"client-only@npm:0.0.1":
  version: 0.0.1
  resolution: "client-only@npm:0.0.1"
  checksum: 0c16bf660dadb90610553c1d8946a7fdfb81d624adea073b8440b7d795d5b5b08beb3c950c6a2cf16279365a3265158a236876d92bce16423c485c322d7dfaf8
  languageName: node
  linkType: hard

"clsx@npm:^2.1.1":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: acd3e1ab9d8a433ecb3cc2f6a05ab95fe50b4a3cfc5ba47abb6cbf3754585fcb87b84e90c822a1f256c4198e3b41c7f6c391577ffc8678ad587fc0976b24fd57
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: ^1.0.0
    simple-swizzle: ^0.2.2
  checksum: c13fe7cff7885f603f49105827d621ce87f4571d78ba28ef4a3f1a104304748f620615e6bf065ecd2145d0d9dad83a3553f52bb25ede7239d18e9f81622f1cc5
  languageName: node
  linkType: hard

"color@npm:^4.2.3":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: ^2.0.1
    color-string: ^1.9.0
  checksum: 0579629c02c631b426780038da929cca8e8d80a40158b09811a0112a107c62e10e4aad719843b791b1e658ab4e800558f2e87ca4522c8b32349d497ecb6adeb4
  languageName: node
  linkType: hard

"colorette@npm:^2.0.20":
  version: 2.0.20
  resolution: "colorette@npm:2.0.20"
  checksum: 0c016fea2b91b733eb9f4bcdb580018f52c0bc0979443dad930e5037a968237ac53d9beb98e218d2e9235834f8eebce7f8e080422d6194e957454255bde71d3d
  languageName: node
  linkType: hard

"commander@npm:^14.0.0":
  version: 14.0.0
  resolution: "commander@npm:14.0.0"
  checksum: 6e9bdaf2e8e4f512855ffc10579eeae2e84c4a7697a91b1a5f62aab3c9849182207855268dd7c3952ae7a2334312a7138f58e929e4b428aef5bf8af862685c9b
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: ab8c07884e42c3a8dbc5dd9592c606176c7eb5c1ca5ff274bcf907039b2c41de3626f684ea75ccf4d361ba004bbaff1f577d5384c155f3871e456bdf27becf9e
  languageName: node
  linkType: hard

"commander@npm:^4.0.0":
  version: 4.1.1
  resolution: "commander@npm:4.1.1"
  checksum: d7b9913ff92cae20cb577a4ac6fcc121bd6223319e54a40f51a14740a681ad5c574fd29a57da478a5f234a6fa6c52cbf0b7c641353e03c648b1ae85ba670b977
  languageName: node
  linkType: hard

"commander@npm:^7.2.0":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 53501cbeee61d5157546c0bef0fedb6cdfc763a882136284bed9a07225f09a14b82d2a84e7637edfd1a679fb35ed9502fd58ef1d091e6287f60d790147f68ddc
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 63ae9933be5a2b8d4509daca5124e20c14d023c820258e484e32dc324d34c2754e71297c94a05784064ad27615037ef677e3f0c00469fb55f409d2bb21261035
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.43.0":
  version: 3.43.0
  resolution: "core-js-compat@npm:3.43.0"
  dependencies:
    browserslist: ^4.25.0
  checksum: 32d1383c3d6bf84b8bb41b5a1e0a45bf8144ea3f7b913a7e83dd51f1252e9db9c187c4e34877b39ac5661be51a03655694c3a01e03637b895c39f7908f440c77
  languageName: node
  linkType: hard

"cosmiconfig@npm:^8.1.3":
  version: 8.3.6
  resolution: "cosmiconfig@npm:8.3.6"
  dependencies:
    import-fresh: ^3.3.0
    js-yaml: ^4.1.0
    parse-json: ^5.2.0
    path-type: ^4.0.0
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: dc339ebea427898c9e03bf01b56ba7afbac07fc7d2a2d5a15d6e9c14de98275a9565da949375aee1809591c152c0a3877bb86dbeaf74d5bd5aaa79955ad9e7a0
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 8d306efacaf6f3f60e0224c287664093fa9185680b2d195852ba9a863f85d02dcc737094c6e512175f8ee0161f9b87c73c6826034c2422e39de7d6569cf4503b
  languageName: node
  linkType: hard

"css-select@npm:^5.1.0":
  version: 5.2.2
  resolution: "css-select@npm:5.2.2"
  dependencies:
    boolbase: ^1.0.0
    css-what: ^6.1.0
    domhandler: ^5.0.2
    domutils: ^3.0.1
    nth-check: ^2.0.1
  checksum: 0ab672620c6bdfe4129dfecf202f6b90f92018b24a1a93cfbb295c24026d0163130ba4b98d7443f87246a2c1d67413798a7a5920cd102b0cfecfbc89896515aa
  languageName: node
  linkType: hard

"css-tree@npm:^2.3.1":
  version: 2.3.1
  resolution: "css-tree@npm:2.3.1"
  dependencies:
    mdn-data: 2.0.30
    source-map-js: ^1.0.1
  checksum: 493cc24b5c22b05ee5314b8a0d72d8a5869491c1458017ae5ed75aeb6c3596637dbe1b11dac2548974624adec9f7a1f3a6cf40593dc1f9185eb0e8279543fbc0
  languageName: node
  linkType: hard

"css-tree@npm:~2.2.0":
  version: 2.2.1
  resolution: "css-tree@npm:2.2.1"
  dependencies:
    mdn-data: 2.0.28
    source-map-js: ^1.0.1
  checksum: b94aa8cc2f09e6f66c91548411fcf74badcbad3e150345074715012d16333ce573596ff5dfca03c2a87edf1924716db765120f94247e919d72753628ba3aba27
  languageName: node
  linkType: hard

"css-what@npm:^6.1.0":
  version: 6.2.2
  resolution: "css-what@npm:6.2.2"
  checksum: 4d1f07b348a638e1f8b4c72804a1e93881f35e0f541256aec5ac0497c5855df7db7ab02da030de950d4813044f6d029a14ca657e0f92c3987e4b604246235b2b
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: f8c4ababffbc5e2ddf2fa9957dda1ee4af6048e22aeda1869d0d00843223c1b13ad3f5d88b51caa46c994225eacb636b764eb807a8883e2fb6f99b4f4e8c48b2
  languageName: node
  linkType: hard

"csso@npm:^5.0.5":
  version: 5.0.5
  resolution: "csso@npm:5.0.5"
  dependencies:
    css-tree: ~2.2.0
  checksum: 0ad858d36bf5012ed243e9ec69962a867509061986d2ee07cc040a4b26e4d062c00d4c07e5ba8d430706ceb02dd87edd30a52b5937fd45b1b6f2119c4993d59a
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 8db785cc92d259102725b3c694ec0c823f5619a84741b5c7991b8ad135dfaa66093038a1cc63e03361a6cd28d122be48f2106ae72334e067dd619a51f49eddf7
  languageName: node
  linkType: hard

"damerau-levenshtein@npm:^1.0.8":
  version: 1.0.8
  resolution: "damerau-levenshtein@npm:1.0.8"
  checksum: d240b7757544460ae0586a341a53110ab0a61126570ef2d8c731e3eab3f0cb6e488e2609e6a69b46727635de49be20b071688698744417ff1b6c1d7ccd03e0de
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-data-view: ^1.0.2
  checksum: 1e1cd509c3037ac0f8ba320da3d1f8bf1a9f09b0be09394b5e40781b8cc15ff9834967ba7c9f843a425b34f9fe14ce44cf055af6662c44263424c1eb8d65659b
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-data-view: ^1.0.2
  checksum: 3600c91ced1cfa935f19ef2abae11029e01738de8d229354d3b2a172bf0d7e4ed08ff8f53294b715569fdf72dfeaa96aa7652f479c0f60570878d88e7e8bddf6
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    is-data-view: ^1.0.1
  checksum: 8dd492cd51d19970876626b5b5169fbb67ca31ec1d1d3238ee6a71820ca8b80cafb141c485999db1ee1ef02f2cc3b99424c5eda8d59e852d9ebb79ab290eb5ee
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4, debug@npm:^4.4.0, debug@npm:^4.4.1":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: ^2.1.3
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: a43826a01cda685ee4cec00fb2d3322eaa90ccadbef60d9287debc2a886be3e835d9199c80070ede75a409ee57828c4c6cd80e4b154f2843f0dc95a570dc0729
  languageName: node
  linkType: hard

"debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: ^2.1.1
  checksum: b3d8c5940799914d30314b7c3304a43305fd0715581a919dacb8b3176d024a782062368405b47491516d2091d6462d4d11f2f4974a405048094f8bfebfa3071c
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"deepmerge@npm:^4.3.1":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 2024c6a980a1b7128084170c4cf56b0fd58a63f2da1660dcfe977415f27b17dbe5888668b59d0b063753f3220719d5e400b7f113609489c90160bb9a5518d052
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    gopd: ^1.0.1
  checksum: 8068ee6cab694d409ac25936eb861eea704b7763f7f342adbdfe337fc27c78d7ae0eff2364b2917b58c508d723c7a074326d068eef2e45c4edcd85cf94d0313b
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3, define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: ^1.0.1
    has-property-descriptors: ^1.0.0
    object-keys: ^1.1.1
  checksum: b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.3":
  version: 2.0.4
  resolution: "detect-libc@npm:2.0.4"
  checksum: 3d186b7d4e16965e10e21db596c78a4e131f9eee69c0081d13b85e6a61d7448d3ba23fe7997648022bdfa3b0eb4cc3c289a44c8188df949445a20852689abef6
  languageName: node
  linkType: hard

"detect-node-es@npm:^1.1.0":
  version: 1.1.0
  resolution: "detect-node-es@npm:1.1.0"
  checksum: e46307d7264644975b71c104b9f028ed1d3d34b83a15b8a22373640ce5ea630e5640b1078b8ea15f202b54641da71e4aa7597093bd4b91f113db520a26a37449
  languageName: node
  linkType: hard

"didyoumean@npm:^1.2.2":
  version: 1.2.2
  resolution: "didyoumean@npm:1.2.2"
  checksum: d5d98719d58b3c2fa59663c4c42ba9716f1fd01245c31d5fce31915bd3aa26e6aac149788e007358f778ebbd68a2256eb5973e8ca6f221df221ba060115acf2e
  languageName: node
  linkType: hard

"dlv@npm:^1.1.3":
  version: 1.1.3
  resolution: "dlv@npm:1.1.3"
  checksum: d7381bca22ed11933a1ccf376db7a94bee2c57aa61e490f680124fa2d1cd27e94eba641d9f45be57caab4f9a6579de0983466f620a2cd6230d7ec93312105ae7
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: ^2.0.2
  checksum: a45e277f7feaed309fe658ace1ff286c6e2002ac515af0aaf37145b8baa96e49899638c7cd47dccf84c3d32abfc113246625b3ac8f552d1046072adee13b0dc8
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: ^2.3.0
    domhandler: ^5.0.2
    entities: ^4.2.0
  checksum: cd1810544fd8cdfbd51fa2c0c1128ec3a13ba92f14e61b7650b5de421b88205fd2e3f0cc6ace82f13334114addb90ed1c2f23074a51770a8e9c1273acbc7f3e6
  languageName: node
  linkType: hard

"domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: ^2.3.0
  checksum: 0f58f4a6af63e6f3a4320aa446d28b5790a009018707bce2859dcb1d21144c7876482b5188395a188dfa974238c019e0a1e610d2fc269a12b2c192ea2b0b131c
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1":
  version: 3.2.2
  resolution: "domutils@npm:3.2.2"
  dependencies:
    dom-serializer: ^2.0.0
    domelementtype: ^2.3.0
    domhandler: ^5.0.3
  checksum: ae941d56f03d857077d55dde9297e960a625229fc2b933187cc4123084d7c2d2517f58283a7336567127029f1e008449bac8ac8506d44341e29e3bb18e02f906
  languageName: node
  linkType: hard

"dot-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "dot-case@npm:3.0.4"
  dependencies:
    no-case: ^3.0.4
    tslib: ^2.0.3
  checksum: a65e3519414856df0228b9f645332f974f2bf5433370f544a681122eab59e66038fc3349b4be1cdc47152779dac71a5864f1ccda2f745e767c46e9c6543b1169
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    es-errors: ^1.3.0
    gopd: ^1.2.0
  checksum: 149207e36f07bd4941921b0ca929e3a28f1da7bd6b6ff8ff7f4e2f2e460675af4576eeba359c635723dc189b64cdd4787e0255897d5b135ccc5d15cb8685fc90
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.173":
  version: 1.5.178
  resolution: "electron-to-chromium@npm:1.5.178"
  checksum: 7799c7c564fbee12fa2b830e3dcac480dd61b1c3eb2e05e802e5b477a1a92c4726ca9046af597b2e8008f2661aa1ab87ca853d36da17a037d172f91dfe874746
  languageName: node
  linkType: hard

"embla-carousel-auto-scroll@npm:^8.6.0":
  version: 8.6.0
  resolution: "embla-carousel-auto-scroll@npm:8.6.0"
  peerDependencies:
    embla-carousel: 8.6.0
  checksum: b843b513d0d40cf4dd3b7c079ef1b84414be8438d30d0d79b52c235fc9cb0c59952ef673db29d1a02094fe7dcbb3563ed8dea8095c9f5bef1fdb2a8b6cda27db
  languageName: node
  linkType: hard

"embla-carousel-autoplay@npm:^8.6.0":
  version: 8.6.0
  resolution: "embla-carousel-autoplay@npm:8.6.0"
  peerDependencies:
    embla-carousel: 8.6.0
  checksum: acb312f7c6fc8bebf13046eb89c874684f69bd6630731720f7d6e45881dfc4c8464734dd2929b1cdca0765fe449c6de310ee3da86e0e01b04cf933bf1bbaf942
  languageName: node
  linkType: hard

"embla-carousel-fade@npm:^8.6.0":
  version: 8.6.0
  resolution: "embla-carousel-fade@npm:8.6.0"
  peerDependencies:
    embla-carousel: 8.6.0
  checksum: 62a5ddc9a51dcbb4071301e8df15de641c782cb19f94891871db89169ba66cdd39358be69f8b2a0cd186ddfacfbce8240b96ffca92cd99d8b1d7a9292903cea8
  languageName: node
  linkType: hard

"embla-carousel-react@npm:^8.6.0":
  version: 8.6.0
  resolution: "embla-carousel-react@npm:8.6.0"
  dependencies:
    embla-carousel: 8.6.0
    embla-carousel-reactive-utils: 8.6.0
  peerDependencies:
    react: ^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  checksum: 30622c3f6bc3de5b608ab3fd82f69ec2ad86e86657c4c9d780380cf159bf5dac0170adced74aecd117c521c06fa014fe71b5fa0ec7c293921fd1cb7dff460be3
  languageName: node
  linkType: hard

"embla-carousel-reactive-utils@npm:8.6.0":
  version: 8.6.0
  resolution: "embla-carousel-reactive-utils@npm:8.6.0"
  peerDependencies:
    embla-carousel: 8.6.0
  checksum: d3663addcb10a5cfecac976987f27fd58f2142ef944da9cd56568120752a71cb04d731e9c396f2e426f43be730f02145a87b88747c6902e24544a6fca9fc0141
  languageName: node
  linkType: hard

"embla-carousel@npm:8.6.0":
  version: 8.6.0
  resolution: "embla-carousel@npm:8.6.0"
  checksum: d943d225d069a8cf0b876440297dda42107e5e69006387c6bce1163b027ac5e5beba67160db818a763c7c46f2103238a097953ac3f383362f8104535cd278de1
  languageName: node
  linkType: hard

"emoji-regex@npm:^10.3.0":
  version: 10.4.0
  resolution: "emoji-regex@npm:10.4.0"
  checksum: a6d9a0e454829a52e664e049847776ee1fff5646617b06cd87de7c03ce1dfcce4102a3b154d5e9c8e90f8125bc120fc1fe114d523dddf60a8a161f26c72658d2
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.17.1":
  version: 5.18.2
  resolution: "enhanced-resolve@npm:5.18.2"
  dependencies:
    graceful-fs: ^4.2.4
    tapable: ^2.2.0
  checksum: af8c0f19cc414f69d7595507576db470c7435f550e7aa1d46292c40aaf1fe03c4d714c495bc31aa927f90887faa8880db428c8bca4dc1595844853ab2195ee25
  languageName: node
  linkType: hard

"entities@npm:^4.2.0, entities@npm:^4.4.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 853f8ebd5b425d350bffa97dd6958143179a5938352ccae092c62d1267c4e392a039be1bae7d51b6e4ffad25f51f9617531fedf5237f15df302ccfb452cbf2d7
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"environment@npm:^1.0.0":
  version: 1.1.0
  resolution: "environment@npm:1.1.0"
  checksum: dd3c1b9825e7f71f1e72b03c2344799ac73f2e9ef81b78ea8b373e55db021786c6b9f3858ea43a436a2c4611052670ec0afe85bc029c384cc71165feee2f4ba6
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: c1c2b8b65f9c91b0f9d75f0debaa7ec5b35c266c2cac5de412c1a6de86d4cbae04ae44e510378cb14d032d0645a36925d0186f8bb7367bcc629db256b743a001
  languageName: node
  linkType: hard

"es-abstract@npm:^1.17.5, es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.3, es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.6, es-abstract@npm:^1.23.9, es-abstract@npm:^1.24.0":
  version: 1.24.0
  resolution: "es-abstract@npm:1.24.0"
  dependencies:
    array-buffer-byte-length: ^1.0.2
    arraybuffer.prototype.slice: ^1.0.4
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    data-view-buffer: ^1.0.2
    data-view-byte-length: ^1.0.2
    data-view-byte-offset: ^1.0.1
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.1.1
    es-set-tostringtag: ^2.1.0
    es-to-primitive: ^1.3.0
    function.prototype.name: ^1.1.8
    get-intrinsic: ^1.3.0
    get-proto: ^1.0.1
    get-symbol-description: ^1.1.0
    globalthis: ^1.0.4
    gopd: ^1.2.0
    has-property-descriptors: ^1.0.2
    has-proto: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    internal-slot: ^1.1.0
    is-array-buffer: ^3.0.5
    is-callable: ^1.2.7
    is-data-view: ^1.0.2
    is-negative-zero: ^2.0.3
    is-regex: ^1.2.1
    is-set: ^2.0.3
    is-shared-array-buffer: ^1.0.4
    is-string: ^1.1.1
    is-typed-array: ^1.1.15
    is-weakref: ^1.1.1
    math-intrinsics: ^1.1.0
    object-inspect: ^1.13.4
    object-keys: ^1.1.1
    object.assign: ^4.1.7
    own-keys: ^1.0.1
    regexp.prototype.flags: ^1.5.4
    safe-array-concat: ^1.1.3
    safe-push-apply: ^1.0.0
    safe-regex-test: ^1.1.0
    set-proto: ^1.0.0
    stop-iteration-iterator: ^1.1.0
    string.prototype.trim: ^1.2.10
    string.prototype.trimend: ^1.0.9
    string.prototype.trimstart: ^1.0.8
    typed-array-buffer: ^1.0.3
    typed-array-byte-length: ^1.0.3
    typed-array-byte-offset: ^1.0.4
    typed-array-length: ^1.0.7
    unbox-primitive: ^1.1.0
    which-typed-array: ^1.1.19
  checksum: 06b3d605e56e3da9d16d4db2629a42dac1ca31f2961a41d15c860422a266115e865b43e82d6b9da81a0fabbbb65ebc12fb68b0b755bc9dbddacb6bf7450e96df
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 0512f4e5d564021c9e3a644437b0155af2679d10d80f21adaf868e64d30efdfbd321631956f20f42d655fedb2e3a027da479fad3fa6048f768eb453a80a5f80a
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: ec1414527a0ccacd7f15f4a3bc66e215f04f595ba23ca75cdae0927af099b5ec865f9f4d33e9d7e86f512f252876ac77d4281a7871531a50678132429b1271b5
  languageName: node
  linkType: hard

"es-iterator-helpers@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-iterator-helpers@npm:1.2.1"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-abstract: ^1.23.6
    es-errors: ^1.3.0
    es-set-tostringtag: ^2.0.3
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.6
    globalthis: ^1.0.4
    gopd: ^1.2.0
    has-property-descriptors: ^1.0.2
    has-proto: ^1.2.0
    has-symbols: ^1.1.0
    internal-slot: ^1.1.0
    iterator.prototype: ^1.1.4
    safe-array-concat: ^1.1.3
  checksum: 952808dd1df3643d67ec7adf20c30b36e5eecadfbf36354e6f39ed3266c8e0acf3446ce9bc465e38723d613cb1d915c1c07c140df65bdce85da012a6e7bda62b
  languageName: node
  linkType: hard

"es-module-lexer@npm:^1.2.1":
  version: 1.7.0
  resolution: "es-module-lexer@npm:1.7.0"
  checksum: 7858bb76ae387fdbf8a6fccc951bf18919768309850587553eca34698b9193fbc65fab03d3d9f69163d860321fbf66adf89d5821e7f4148c7cb7d7b997259211
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: ^1.3.0
  checksum: 214d3767287b12f36d3d7267ef342bbbe1e89f899cfd67040309fc65032372a8e60201410a99a1645f2f90c1912c8c49c8668066f6bdd954bcd614dda2e3da97
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.3, es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
    has-tostringtag: ^1.0.2
    hasown: ^2.0.2
  checksum: 789f35de4be3dc8d11fdcb91bc26af4ae3e6d602caa93299a8c45cf05d36cc5081454ae2a6d3afa09cceca214b76c046e4f8151e092e6fc7feeb5efb9e794fc6
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.2, es-shim-unscopables@npm:^1.1.0":
  version: 1.1.0
  resolution: "es-shim-unscopables@npm:1.1.0"
  dependencies:
    hasown: ^2.0.2
  checksum: 33cfb1ebcb2f869f0bf528be1a8660b4fe8b6cec8fc641f330e508db2284b58ee2980fad6d0828882d22858c759c0806076427a3673b6daa60f753e3b558ee15
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: ^1.2.7
    is-date-object: ^1.0.5
    is-symbol: ^1.0.4
  checksum: 966965880356486cd4d1fe9a523deda2084c81b3702d951212c098f5f2ee93605d1b7c1840062efb48a07d892641c7ed1bc194db563645c0dd2b919cb6d65b93
  languageName: node
  linkType: hard

"escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 47b029c83de01b0d17ad99ed766347b974b0d628e848de404018f3abee728e987da0d2d370ad4574aa3d5b5bfc368754fd085d69a30f8e75903486ec4b5b709e
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-config-next@npm:15.1.7":
  version: 15.1.7
  resolution: "eslint-config-next@npm:15.1.7"
  dependencies:
    "@next/eslint-plugin-next": 15.1.7
    "@rushstack/eslint-patch": ^1.10.3
    "@typescript-eslint/eslint-plugin": ^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0
    "@typescript-eslint/parser": ^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0
    eslint-import-resolver-node: ^0.3.6
    eslint-import-resolver-typescript: ^3.5.2
    eslint-plugin-import: ^2.31.0
    eslint-plugin-jsx-a11y: ^6.10.0
    eslint-plugin-react: ^7.37.0
    eslint-plugin-react-hooks: ^5.0.0
  peerDependencies:
    eslint: ^7.23.0 || ^8.0.0 || ^9.0.0
    typescript: ">=3.3.1"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 54a5facbd4c9bf473bdb29416036bfe508d198de223cad05763d5708d0d1f2a7cdab984796db6e8d187568831436c7a4fd1657f12c938a05d55944db78af14fe
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^10.1.5":
  version: 10.1.5
  resolution: "eslint-config-prettier@npm:10.1.5"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 6d8eb41c716fc2b073bfd9daede0c87c4c2b29055f15c94618eec00a19c5499024ddb89902d6793b5ac9102075e4739d7cc2d4d58c7e7ed4cc2dcf79e3fbcacc
  languageName: node
  linkType: hard

"eslint-import-resolver-node@npm:^0.3.6, eslint-import-resolver-node@npm:^0.3.9":
  version: 0.3.9
  resolution: "eslint-import-resolver-node@npm:0.3.9"
  dependencies:
    debug: ^3.2.7
    is-core-module: ^2.13.0
    resolve: ^1.22.4
  checksum: 439b91271236b452d478d0522a44482e8c8540bf9df9bd744062ebb89ab45727a3acd03366a6ba2bdbcde8f9f718bab7fe8db64688aca75acf37e04eafd25e22
  languageName: node
  linkType: hard

"eslint-import-resolver-typescript@npm:^3.5.2":
  version: 3.10.1
  resolution: "eslint-import-resolver-typescript@npm:3.10.1"
  dependencies:
    "@nolyfill/is-core-module": 1.0.39
    debug: ^4.4.0
    get-tsconfig: ^4.10.0
    is-bun-module: ^2.0.0
    stable-hash: ^0.0.5
    tinyglobby: ^0.2.13
    unrs-resolver: ^1.6.2
  peerDependencies:
    eslint: "*"
    eslint-plugin-import: "*"
    eslint-plugin-import-x: "*"
  peerDependenciesMeta:
    eslint-plugin-import:
      optional: true
    eslint-plugin-import-x:
      optional: true
  checksum: 57acb58fe28257024236b52ebfe6a3d2e3970a88002e02e771ff327c850c76b2a6b90175b54a980e9efe4787ac09bafe53cb3ebabf3fd165d3ff2a80b2d7e50d
  languageName: node
  linkType: hard

"eslint-module-utils@npm:^2.12.1":
  version: 2.12.1
  resolution: "eslint-module-utils@npm:2.12.1"
  dependencies:
    debug: ^3.2.7
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: 2f074670d8c934687820a83140048776b28bbaf35fc37f35623f63cc9c438d496d11f0683b4feabb9a120435435d4a69604b1c6c567f118be2c9a0aba6760fc1
  languageName: node
  linkType: hard

"eslint-plugin-import@npm:^2.31.0":
  version: 2.32.0
  resolution: "eslint-plugin-import@npm:2.32.0"
  dependencies:
    "@rtsao/scc": ^1.1.0
    array-includes: ^3.1.9
    array.prototype.findlastindex: ^1.2.6
    array.prototype.flat: ^1.3.3
    array.prototype.flatmap: ^1.3.3
    debug: ^3.2.7
    doctrine: ^2.1.0
    eslint-import-resolver-node: ^0.3.9
    eslint-module-utils: ^2.12.1
    hasown: ^2.0.2
    is-core-module: ^2.16.1
    is-glob: ^4.0.3
    minimatch: ^3.1.2
    object.fromentries: ^2.0.8
    object.groupby: ^1.0.3
    object.values: ^1.2.1
    semver: ^6.3.1
    string.prototype.trimend: ^1.0.9
    tsconfig-paths: ^3.15.0
  peerDependencies:
    eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
  checksum: 8cd40595b5e4346d3698eb577014b4b6d0ba57b7b9edf975be4f052a89330ec202d0cc5c3861d37ebeafa151b6264821410243889b0c31710911a6b625bcf76b
  languageName: node
  linkType: hard

"eslint-plugin-jsx-a11y@npm:^6.10.0":
  version: 6.10.2
  resolution: "eslint-plugin-jsx-a11y@npm:6.10.2"
  dependencies:
    aria-query: ^5.3.2
    array-includes: ^3.1.8
    array.prototype.flatmap: ^1.3.2
    ast-types-flow: ^0.0.8
    axe-core: ^4.10.0
    axobject-query: ^4.1.0
    damerau-levenshtein: ^1.0.8
    emoji-regex: ^9.2.2
    hasown: ^2.0.2
    jsx-ast-utils: ^3.3.5
    language-tags: ^1.0.9
    minimatch: ^3.1.2
    object.fromentries: ^2.0.8
    safe-regex-test: ^1.0.3
    string.prototype.includes: ^2.0.1
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9
  checksum: 0cc861398fa26ada61ed5703eef5b335495fcb96253263dcd5e399488ff019a2636372021baacc040e3560d1a34bfcd5d5ad9f1754f44cd0509c956f7df94050
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:^5.2.3":
  version: 5.5.1
  resolution: "eslint-plugin-prettier@npm:5.5.1"
  dependencies:
    prettier-linter-helpers: ^1.0.0
    synckit: ^0.11.7
  peerDependencies:
    "@types/eslint": ">=8.0.0"
    eslint: ">=8.0.0"
    eslint-config-prettier: ">= 7.0.0 <10.0.0 || >=10.1.0"
    prettier: ">=3.0.0"
  peerDependenciesMeta:
    "@types/eslint":
      optional: true
    eslint-config-prettier:
      optional: true
  checksum: 913ecebfeab932ed510d8608875594d2529dc28957eed0d56a896e9862257031038a9f0921dcf198d61475ef0f5904332a7f1839be387d0a314c91f3cfbf3817
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:^5.0.0, eslint-plugin-react-hooks@npm:^5.2.0":
  version: 5.2.0
  resolution: "eslint-plugin-react-hooks@npm:5.2.0"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0
  checksum: 5920736a78c0075488e7e30e04fbe5dba5b6b5a6c8c4b5742fdae6f9b8adf4ee387bc45dc6e03b4012865e6fd39d134da7b83a40f57c90cc9eecf80692824e3a
  languageName: node
  linkType: hard

"eslint-plugin-react@npm:^7.37.0, eslint-plugin-react@npm:^7.37.5":
  version: 7.37.5
  resolution: "eslint-plugin-react@npm:7.37.5"
  dependencies:
    array-includes: ^3.1.8
    array.prototype.findlast: ^1.2.5
    array.prototype.flatmap: ^1.3.3
    array.prototype.tosorted: ^1.1.4
    doctrine: ^2.1.0
    es-iterator-helpers: ^1.2.1
    estraverse: ^5.3.0
    hasown: ^2.0.2
    jsx-ast-utils: ^2.4.1 || ^3.0.0
    minimatch: ^3.1.2
    object.entries: ^1.1.9
    object.fromentries: ^2.0.8
    object.values: ^1.2.1
    prop-types: ^15.8.1
    resolve: ^2.0.0-next.5
    semver: ^6.3.1
    string.prototype.matchall: ^4.0.12
    string.prototype.repeat: ^1.0.0
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7
  checksum: 8675e7558e646e3c2fcb04bb60cfe416000b831ef0b363f0117838f5bfc799156113cb06058ad4d4b39fc730903b7360b05038da11093064ca37caf76b7cf2ca
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^4.1.1
  checksum: 47e4b6a3f0cc29c7feedee6c67b225a2da7e155802c6ea13bbef4ac6b9e10c66cd2dcb987867ef176292bf4e64eccc680a49e35e9e9c669f4a02bac17e86abdb
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.4.0":
  version: 8.4.0
  resolution: "eslint-scope@npm:8.4.0"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: cf88f42cd5e81490d549dc6d350fe01e6fe420f9d9ea34f134bb359b030e3c4ef888d36667632e448937fe52449f7181501df48c08200e3d3b0fee250d05364e
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 36e9ef87fca698b6fd7ca5ca35d7b2b6eeaaf106572e2f7fd31c12d3bfdaccdb587bba6d3621067e5aece31c8c3a348b93922ab8f7b2cbc6aaab5e1d89040c60
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.1":
  version: 4.2.1
  resolution: "eslint-visitor-keys@npm:4.2.1"
  checksum: 3a77e3f99a49109f6fb2c5b7784bc78f9743b834d238cdba4d66c602c6b52f19ed7bcd0a5c5dbbeae3a8689fd785e76c001799f53d2228b278282cf9f699fff5
  languageName: node
  linkType: hard

"eslint@npm:^9.26.0":
  version: 9.30.1
  resolution: "eslint@npm:9.30.1"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@eslint-community/regexpp": ^4.12.1
    "@eslint/config-array": ^0.21.0
    "@eslint/config-helpers": ^0.3.0
    "@eslint/core": ^0.14.0
    "@eslint/eslintrc": ^3.3.1
    "@eslint/js": 9.30.1
    "@eslint/plugin-kit": ^0.3.1
    "@humanfs/node": ^0.16.6
    "@humanwhocodes/module-importer": ^1.0.1
    "@humanwhocodes/retry": ^0.4.2
    "@types/estree": ^1.0.6
    "@types/json-schema": ^7.0.15
    ajv: ^6.12.4
    chalk: ^4.0.0
    cross-spawn: ^7.0.6
    debug: ^4.3.2
    escape-string-regexp: ^4.0.0
    eslint-scope: ^8.4.0
    eslint-visitor-keys: ^4.2.1
    espree: ^10.4.0
    esquery: ^1.5.0
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^8.0.0
    find-up: ^5.0.0
    glob-parent: ^6.0.2
    ignore: ^5.2.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    json-stable-stringify-without-jsonify: ^1.0.1
    lodash.merge: ^4.6.2
    minimatch: ^3.1.2
    natural-compare: ^1.4.0
    optionator: ^0.9.3
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: e6723b98ba19ff17cf0cacb29c3c0ea5c7c6b6fb648136b2d009e7e2da4980a2562c9523623b0faf449750e890f3b274b20bee11fa9c8f43362d235485ba2f91
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.4.0":
  version: 10.4.0
  resolution: "espree@npm:10.4.0"
  dependencies:
    acorn: ^8.15.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^4.2.1
  checksum: 5f9d0d7c81c1bca4bfd29a55270067ff9d575adb8c729a5d7f779c2c7b910bfc68ccf8ec19b29844b707440fc159a83868f22c8e87bbf7cbcb225ed067df6c85
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: 08ec4fe446d9ab27186da274d979558557fbdbbd10968fa9758552482720c54152a5640e08b9009e5a30706b66aba510692054d4129d32d0e12e05bbc0b96fb2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: a6299491f9940bb246124a8d44b7b7a413a8336f5436f9837aaa9330209bd9ee8af7e91a654a3545aee9c54b3308e78ee360cef1d777d37cfef77d2fa33b5827
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0, estraverse@npm:^5.3.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"eventemitter3@npm:^5.0.1":
  version: 5.0.1
  resolution: "eventemitter3@npm:5.0.1"
  checksum: 543d6c858ab699303c3c32e0f0f47fc64d360bf73c3daf0ac0b5079710e340d6fe9f15487f94e66c629f5f82cd1a8678d692f3dbb6f6fcd1190e1b97fcad36f8
  languageName: node
  linkType: hard

"events@npm:^3.2.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: f6f487ad2198aa41d878fa31452f1a3c00958f46e9019286ff4787c84aac329332ab45c9cdc8c445928fc6d7ded294b9e005a7fce9426488518017831b272780
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 7e191e3dd6edd8c56c88f2c8037c98fbb8034fe48778be53ed8cb30ccef371a061a4e999a469aab939b92f8f12698f3b426d52f4f76b7a20da5f9f98c3cbc862
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: d22d371b994fdc8cce9ff510d7b8dc4da70ac327bcba20df607dd5b9cae9f908f4d1028f5fe467650f058d1e7270235ae0b8230809a262b4df587a3b3aa216c3
  languageName: node
  linkType: hard

"fast-glob@npm:3.3.1":
  version: 3.3.1
  resolution: "fast-glob@npm:3.3.1"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.4
  checksum: b6f3add6403e02cf3a798bfbb1183d0f6da2afd368f27456010c0bc1f9640aea308243d4cb2c0ab142f618276e65ecb8be1661d7c62a7b4e5ba774b9ce5432e5
  languageName: node
  linkType: hard

"fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.8
  checksum: 0704d7b85c0305fd2cef37777337dfa26230fdd072dce9fb5c82a4b03156f3ffb8ed3e636033e65d45d2a5805a4e475825369a27404c0307f2db0c8eb3366fbd
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.6
  resolution: "fast-uri@npm:3.0.6"
  checksum: 7161ba2a7944778d679ba8e5f00d6a2bb479a2142df0982f541d67be6c979b17808f7edbb0ce78161c85035974bde3fa52b5137df31da46c0828cb629ba67c4e
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: ^1.0.4
  checksum: 7691d1794fb84ad0ec2a185f10e00f0e1713b894e2c9c4d42f0bc0ba5f8c00e6e655a202074ca0b91b9c3d977aab7c30c41a8dc069fb5368576ac0054870a0e6
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.6
  resolution: "fdir@npm:6.4.6"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: fe9f3014901d023cf631831dcb9eae5447f4d7f69218001dd01ecf007eccc40f6c129a04411b5cc273a5f93c14e02e971e17270afc9022041c80be924091eb6f
  languageName: node
  linkType: hard

"fetch-event-stream@npm:^0.1.5":
  version: 0.1.5
  resolution: "fetch-event-stream@npm:0.1.5"
  checksum: 06411c4ab6f86bd3093fab79c92e14083cf0e15f91e6546a38ed9d6eff48f9d06e48e19c9e371b1f0317d93a21eef7c65d600b20120d15eca7e66e2aeb91d04c
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: ^4.0.0
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b4abfbca3839a3d55e4ae5ec62e131e2e356bf4859ce8480c64c4876100f4df292a63e5bb1618e1d7460282ca2b305653064f01654474aa35c68000980f17798
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: ^6.0.0
    path-exists: ^4.0.0
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: ^3.2.9
    keyv: ^4.5.4
  checksum: 899fc86bf6df093547d76e7bfaeb900824b869d7d457d02e9b8aae24836f0a99fbad79328cfd6415ee8908f180699bf259dc7614f793447cb14f707caf5996f6
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: ^1.2.7
  checksum: 3c986d7e11f4381237cc98baa0a2f87eabe74719eee65ed7bed275163082b940ede19268c61d04c6260e0215983b12f8d885e3c8f9aa8c2113bf07c37051745c
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: ^7.0.6
    signal-exit: ^4.0.1
  checksum: b2c1a6fc0bf0233d645d9fefdfa999abf37db1b33e5dab172b3cbfb0662b88bfbd2c9e7ab853533d199050ec6b65c03fcf078fc212d26e4990220e98c6930eef
  languageName: node
  linkType: hard

"fraction.js@npm:^4.3.7":
  version: 4.3.7
  resolution: "fraction.js@npm:4.3.7"
  checksum: e1553ae3f08e3ba0e8c06e43a3ab20b319966dfb7ddb96fd9b5d0ee11a66571af7f993229c88ebbb0d4a816eb813a24ed48207b140d442a8f76f33763b8d1f3f
  languageName: node
  linkType: hard

"framer-motion@npm:^12.23.0":
  version: 12.23.0
  resolution: "framer-motion@npm:12.23.0"
  dependencies:
    motion-dom: ^12.22.0
    motion-utils: ^12.19.0
    tslib: ^2.4.0
  peerDependencies:
    "@emotion/is-prop-valid": "*"
    react: ^18.0.0 || ^19.0.0
    react-dom: ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@emotion/is-prop-valid":
      optional: true
    react:
      optional: true
    react-dom:
      optional: true
  checksum: 0f5f18bc66b11f6d9b7b294cf7723479f0a313d1afcbab54786ed25e40da49734f53b960a4abe95d08b138736f84c11cacd007113adf755217b6a6200d49ad78
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 8722a41109130851d979222d3ec88aabaceeaaf8f57b2a8f744ef8bd2d1ce95453b04a61daa0078822bc5cd21e008814f06fe6586f56fef511e71b8d2394d802
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: latest
  checksum: 11e6ea6fea15e42461fc55b4b0e4a0a3c654faa567f1877dbd353f39156f69def97a69936d1746619d656c4b93de2238bf731f6085a03a50cabf287c9d024317
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#~builtin<compat/fsevents>::version=2.3.3&hash=18f3a7"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 2b0ff4ce708d99715ad14a6d1f894e2a83242e4a52ccfcefaee5e40050562e5f6dafc1adbb4ce2d4ab47279a45dc736ab91ea5042d843c3c092820dfe032efb1
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    functions-have-names: ^1.2.3
    hasown: ^2.0.2
    is-callable: ^1.2.7
  checksum: 3a366535dc08b25f40a322efefa83b2da3cd0f6da41db7775f2339679120ef63b6c7e967266182609e655b8f0a8f65596ed21c7fd72ad8bd5621c2340edd4010
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: c3f1f5ba20f4e962efb71344ce0a40722163e85bee2101ce25f88214e78182d2d2476aa85ef37950c579eb6cf6ee811c17b3101bb84004bb75655f3e33f3fdb5
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: a7437e58c6be12aa6c90f7730eac7fa9833dc78872b4ad2963d2031b00a3367a93f98aec75f9aaac7220848e4026d67a8655e870b24f20a543d103c0d65952ec
  languageName: node
  linkType: hard

"get-east-asian-width@npm:^1.0.0":
  version: 1.3.0
  resolution: "get-east-asian-width@npm:1.3.0"
  checksum: 757a34c7a46ff385e2775f96f9d3e553f6b6666a8898fb89040d36a1010fba692332772945606a7d4b0f0c6afb84cd394e75d5477c56e1f00f1eb79603b0aecc
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: ^1.0.2
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.1.1
    function-bind: ^1.1.2
    get-proto: ^1.0.1
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    math-intrinsics: ^1.1.0
  checksum: 301008e4482bb9a9cb49e132b88fee093bff373b4e6def8ba219b1e96b60158a6084f273ef5cafe832e42cd93462f4accb46a618d35fe59a2b507f2388c5b79d
  languageName: node
  linkType: hard

"get-nonce@npm:^1.0.0":
  version: 1.0.1
  resolution: "get-nonce@npm:1.0.1"
  checksum: e2614e43b4694c78277bb61b0f04583d45786881289285c73770b07ded246a98be7e1f78b940c80cbe6f2b07f55f0b724e6db6fd6f1bcbd1e8bdac16521074ed
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: ^1.0.1
    es-object-atoms: ^1.0.0
  checksum: 4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
  checksum: 655ed04db48ee65ef2ddbe096540d4405e79ba0a7f54225775fef43a7e2afcb93a77d141c5f05fdef0afce2eb93bcbfb3597142189d562ac167ff183582683cd
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.10.0":
  version: 4.10.1
  resolution: "get-tsconfig@npm:4.10.1"
  dependencies:
    resolve-pkg-maps: ^1.0.0
  checksum: 22925debda6bd0992171a44ee79a22c32642063ba79534372c4d744e0c9154abe2c031659da0fb86bc9e73fc56a3b76b053ea5d24ca3ac3da43d2e6f7d1c3c33
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.4.1":
  version: 0.4.1
  resolution: "glob-to-regexp@npm:0.4.1"
  checksum: e795f4e8f06d2a15e86f76e4d92751cf8bbfcf0157cea5c2f0f35678a8195a750b34096b1256e436f0cebc1883b5ff0888c47348443e69546a5a87f9e1eb1167
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^3.1.2
    minimatch: ^9.0.4
    minipass: ^7.1.2
    package-json-from-dist: ^1.0.0
    path-scurry: ^1.11.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 0bc725de5e4862f9f387fd0f2b274baf16850dcd2714502ccf471ee401803997983e2c05590cb65f9675a3c6f2a58e7a53f9e365704108c6ad3cbf1d60934c4a
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 534b8216736a5425737f59f6e6a5c7f386254560c9f41d24a9227d60ee3ad4a9e82c5b85def0e212e9d92162f83a92544be4c7fd4c902cb913736c10e08237ac
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: ^1.2.1
    gopd: ^1.0.1
  checksum: 39ad667ad9f01476474633a1834a70842041f70a55571e8dcef5fb957980a92da5022db5430fca8aecc5d47704ae30618c0bc877a579c70710c904e9ef06108a
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: cc6d8e655e360955bdccaca51a12a474268f95bb793fc3e1f2bdadb075f28bfd1fd988dab872daf77a61d78cbaf13744bc8727a17cfb1d150d76047d805375f3
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.2.11, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: bab8f0be9b568857c7bec9fda95a89f87b783546d02951c40c33f84d05bb7da3fd10f863a9beb901463669b6583173a8c8cc6d6b306ea2b9b9d5d3d943c3a673
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 79730518ae02c77e4af6a1d1a0b6a2c3e1509785532771f9baf0241e83e36329542c3d7a0e723df8cbc85f74eff4f177828a2265a01ba576adbdc2d40d86538b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: ^1.0.0
  checksum: fcbb246ea2838058be39887935231c6d5788babed499d0e9d0cc5737494c48aba4fe17ba1449e0d0fbbb1e36175442faa37f9c427ae357d6ccb1d895fbcd3de3
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: ^1.0.0
  checksum: f55010cb94caa56308041d77967c72a02ffd71386b23f9afa8447e58bc92d49d15c19bf75173713468e92fe3fb1680b03b115da39c21c32c74886d1d50d3e7ff
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: b2316c7302a0e8ba3aaba215f834e96c22c86f192e7310bdf689dd0e6999510c89b00fbc5742571507cebf25764d68c988b3a0da217369a73596191ac0ce694b
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: ^1.0.3
  checksum: 999d60bb753ad714356b2c6c87b7fb74f32463b8426e159397da4bde5bca7e598ab1073f4d8d4deafac297f2eb311484cd177af242776bf05f0d11565680468d
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: e8516f776a15149ca6c6ed2ae3110c417a00b62260e222590e54aa367cbcd6ed99122020b37b7fbdf05748df57b265e70095d7bf35a47660587619b15ffb93db
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 7a7246ddfce629f96832791176fd643589d954e6f3b49548dadb4290451961237fab8fcea41cd2008fe819d95b41c1e8b97f47d088afc0a1c81705287b4ddbcc
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: ^7.1.0
    debug: ^4.3.4
  checksum: 670858c8f8f3146db5889e1fa117630910101db601fff7d5a8aa637da0abedf68c899f03d3451cac2f83bcc4c3d2dabf339b3aa00ff8080571cceb02c3ce02f3
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: ^7.1.2
    debug: 4
  checksum: b882377a120aa0544846172e5db021fa8afbf83fea2a897d397bd2ddd8095ab268c24bc462f40a15f2a8c600bf4aa05ce52927f70038d4014e68aefecfa94e8d
  languageName: node
  linkType: hard

"husky@npm:^9.1.7":
  version: 9.1.7
  resolution: "husky@npm:9.1.7"
  bin:
    husky: bin.js
  checksum: c2412753f15695db369634ba70f50f5c0b7e5cb13b673d0826c411ec1bd9ddef08c1dad89ea154f57da2521d2605bd64308af748749b27d08c5f563bcd89975f
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 2acfd32a573260ea522ea0bfeff880af426d68f6831f973129e2ba7363f422923cf53aab62f8369cbf4667c7b25b6f8a3761b34ecdb284ea18e87a5262a865be
  languageName: node
  linkType: hard

"ignore@npm:^7.0.0":
  version: 7.0.5
  resolution: "ignore@npm:7.0.5"
  checksum: d0862bf64d3d58bf34d5fb0a9f725bec9ca5ce8cd1aecc8f28034269e8f69b8009ffd79ca3eda96962a6a444687781cd5efdb8c7c8ddc0a6996e36d31c217f14
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1, import-fresh@npm:^3.3.0":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: a06b19461b4879cc654d46f8a6244eb55eb053437afd4cbb6613cad6be203811849ed3e4ea038783092879487299fda24af932b86bdfff67c9055ba3612b8c87
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    hasown: ^2.0.2
    side-channel: ^1.1.0
  checksum: 8e0991c2d048cc08dab0a91f573c99f6a4215075887517ea4fa32203ce8aea60fa03f95b177977fa27eb502e5168366d0f3e02c762b799691411d49900611861
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: 1.1.0
    sprintf-js: ^1.1.3
  checksum: aa15f12cfd0ef5e38349744e3654bae649a34c3b10c77a674a167e99925d1549486c5b14730eebce9fea26f6db9d5e42097b00aa4f9f612e68c79121c71652dc
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    get-intrinsic: ^1.2.6
  checksum: f137a2a6e77af682cdbffef1e633c140cf596f72321baf8bba0f4ef22685eb4339dde23dfe9e9ca430b5f961dee4d46577dcf12b792b68518c8449b134fb9156
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: eef4417e3c10e60e2c810b6084942b3ead455af16c4509959a27e490e7aee87cfb3f38e01bbde92220b528a0ee1a18d52b787e1458ee86174d8c7f0e58cd488f
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 977e64f54d91c8f169b59afcd80ff19227e9f5c791fa28fa2e5bce355cbaf6c2c356711b734656e80c9dd4a854dd7efcf7894402f1031dfc5de5d620775b4d5f
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: ^1.0.0
    call-bound: ^1.0.3
    get-proto: ^1.0.1
    has-tostringtag: ^1.0.2
    safe-regex-test: ^1.1.0
  checksum: 9bece45133da26636488ca127d7686b85ad3ca18927e2850cff1937a650059e90be1c71a48623f8791646bb7a241b0cabf602a0b9252dcfa5ab273f2399000e6
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: ^1.0.2
  checksum: ee1544f0e664f253306786ed1dce494b8cf242ef415d6375d8545b4d8816b0f054bd9f948a8988ae2c6325d1c28260dd02978236b2f7b8fb70dfc4838a6c9fa7
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: 84192eb88cff70d320426f35ecd63c3d6d495da9d805b19bc65b518984b7c0760280e57dbf119b7e9be6b161784a5a673ab2c6abe83abb5198a432232ad5b35c
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 0415b181e8f1bfd5d3f8a20f8108e64d372a72131674eea9c2923f39d065b6ad08d654765553bdbffbd92c3746f1007986c34087db1bd89a31f71be8359ccdaa
  languageName: node
  linkType: hard

"is-bun-module@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-bun-module@npm:2.0.0"
  dependencies:
    semver: ^7.7.1
  checksum: e75bd87cb1aaff7c97cf085509669559a713f741a43b4fd5979cb44c5c0c16c05670ce5f23fc22337d1379211fac118c525c5ed73544076ddaf181c1c21ace35
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 61fd57d03b0d984e2ed3720fb1c7a897827ea174bd44402878e059542ea8c4aeedee0ea0985998aa5cc2736b2fa6e271c08587addb5b3959ac52cf665173d1ac
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0, is-core-module@npm:^2.16.0, is-core-module@npm:^2.16.1":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: ^2.0.2
  checksum: 6ec5b3c42d9cbf1ac23f164b16b8a140c3cec338bf8f884c076ca89950c7cc04c33e78f02b8cae7ff4751f3247e3174b2330f1fe4de194c7210deb8b1ea316a7
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    get-intrinsic: ^1.2.6
    is-typed-array: ^1.1.13
  checksum: 31600dd19932eae7fd304567e465709ffbfa17fa236427c9c864148e1b54eb2146357fcf3aed9b686dee13c217e1bb5a649cb3b9c479e1004c0648e9febde1b2
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.2
    has-tostringtag: ^1.0.2
  checksum: d6c36ab9d20971d65f3fc64cef940d57a4900a2ac85fb488a46d164c2072a33da1cb51eefcc039e3e5c208acbce343d3480b84ab5ff0983f617512da2742562a
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
  checksum: 38c646c506e64ead41a36c182d91639833311970b6b6c6268634f109eef0a1a9d2f1f2e499ef4cb43c744a13443c4cdd2f0812d5afdcee5e9b65b72b28c48557
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^4.0.0":
  version: 4.0.0
  resolution: "is-fullwidth-code-point@npm:4.0.0"
  checksum: 8ae89bf5057bdf4f57b346fb6c55e9c3dd2549983d54191d722d5c739397a903012cc41a04ee3403fd872e811243ef91a7c5196da7b5841dc6b6aae31a264a8d
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^5.0.0":
  version: 5.0.0
  resolution: "is-fullwidth-code-point@npm:5.0.0"
  dependencies:
    get-east-asian-width: ^1.0.0
  checksum: 8dfb2d2831b9e87983c136f5c335cd9d14c1402973e357a8ff057904612ed84b8cba196319fabedf9aefe4639e14fe3afe9d9966d1d006ebeb40fe1fed4babe5
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    get-proto: ^1.0.0
    has-tostringtag: ^1.0.2
    safe-regex-test: ^1.1.0
  checksum: f7f7276131bdf7e28169b86ac55a5b080012a597f9d85a0cbef6fe202a7133fa450a3b453e394870e3cb3685c5a764c64a9f12f614684b46969b1e6f297bed6b
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: e6ce5f6380f32b141b3153e6ba9074892bbbbd655e92e7ba5ff195239777e767a976dcd4e22f864accaf30e53ebf961ab1995424aef91af68788f0591b7396cc
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-negative-zero@npm:2.0.3"
  checksum: c1e6b23d2070c0539d7b36022d5a94407132411d01aba39ec549af824231f3804b1aea90b5e4e58e807a65d23ceb538ed6e355ce76b267bdd86edb757ffcbdcd
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 6517f0a0e8c4b197a21afb45cd3053dc711e79d45d8878aa3565de38d0102b130ca8732485122c7b336e98c27dacd5236854e3e6526e0eb30cae64956535662f
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: ^1.0.2
    gopd: ^1.2.0
    has-tostringtag: ^1.0.2
    hasown: ^2.0.2
  checksum: 99ee0b6d30ef1bb61fa4b22fae7056c6c9b3c693803c0c284ff7a8570f83075a7d38cda53b06b7996d441215c27895ea5d1af62124562e13d91b3dbec41a5e13
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 36e3f8c44bdbe9496c9689762cc4110f6a6a12b767c5d74c0398176aa2678d4467e3bf07595556f2dba897751bde1422480212b97d973c7b08a343100b0c0dfe
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: ^1.0.3
  checksum: 1611fedc175796eebb88f4dfc393dd969a4a8e6c69cadaff424ee9d4464f9f026399a5f84a90f7c62d6d7ee04e3626a912149726de102b0bd6c1ee6a9868fa5a
  languageName: node
  linkType: hard

"is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 2eeaaff605250f5e836ea3500d33d1a5d3aa98d008641d9d42fb941e929ffd25972326c2ef912987e54c95b6f10416281aaf1b35cdf81992cfb7524c5de8e193
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.2
    has-symbols: ^1.1.0
    safe-regex-test: ^1.1.0
  checksum: bfafacf037af6f3c9d68820b74be4ae8a736a658a3344072df9642a090016e281797ba8edbeb1c83425879aae55d1cb1f30b38bf132d703692b2570367358032
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: ^1.1.16
  checksum: ea7cfc46c282f805d19a9ab2084fd4542fed99219ee9dbfbc26284728bd713a51eac66daa74eca00ae0a43b61322920ba334793607dc39907465913e921e0892
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: f36aef758b46990e0d3c37269619c0a08c5b29428c0bb11ecba7f75203442d6c7801239c2f31314bc79199217ef08263787f3837d9e22610ad1da62970d6616d
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
  checksum: 1769b9aed5d435a3a989ffc18fc4ad1947d2acdaf530eb2bd6af844861b545047ea51102f75901f89043bed0267ed61d914ee21e6e8b9aa734ec201cdfc0726f
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: ^1.0.3
    get-intrinsic: ^1.2.6
  checksum: 5c6c8415a06065d78bdd5e3a771483aa1cd928df19138aa73c4c51333226f203f22117b4325df55cc8b3085a6716870a320c2d757efee92d7a7091a039082041
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: bd5bbe4104438c4196ba58a54650116007fa0262eccef13a4c55b2e09a5b36b59f1e75b9fcc49883dd9d4953892e6fc007eef9e9155648ceea036e184b0f930a
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"iterator.prototype@npm:^1.1.4":
  version: 1.1.5
  resolution: "iterator.prototype@npm:1.1.5"
  dependencies:
    define-data-property: ^1.1.4
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.6
    get-proto: ^1.0.0
    has-symbols: ^1.1.0
    set-function-name: ^2.0.2
  checksum: 7db23c42629ba4790e6e15f78b555f41dbd08818c85af306988364bd19d86716a1187cb333444f3a0036bfc078a0e9cb7ec67fef3a61662736d16410d7f77869
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: be31027fc72e7cc726206b9f560395604b82e0fddb46c4cbf9f97d049bcef607491a5afc0699612eaa4213ca5be8fd3e1e7cd187b3040988b65c9489838a7c00
  languageName: node
  linkType: hard

"jest-worker@npm:^27.4.5":
  version: 27.5.1
  resolution: "jest-worker@npm:27.5.1"
  dependencies:
    "@types/node": "*"
    merge-stream: ^2.0.0
    supports-color: ^8.0.0
  checksum: 98cd68b696781caed61c983a3ee30bf880b5bd021c01d98f47b143d4362b85d0737f8523761e2713d45e18b4f9a2b98af1eaee77afade4111bb65c77d6f7c980
  languageName: node
  linkType: hard

"jiti@npm:^1.21.6":
  version: 1.21.7
  resolution: "jiti@npm:1.21.7"
  bin:
    jiti: bin/jiti.js
  checksum: 9cd20dabf82e3a4cceecb746a69381da7acda93d34eed0cdb9c9bdff3bce07e4f2f4a016ca89924392c935297d9aedc58ff9f7d3281bc5293319ad244926e0b7
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c7830dfd456c3ef2c6e355cc5a92e6700ceafa1d14bba54497b34a99f0376cecbb3e9ac14d3e5849b426d5a5140709a66237a8c991c675431271c4ce5504151a
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 944f924f2bd67ad533b3850eee47603eed0f6ae425fd1ee8c760f477e8c34a05f144c1bd4f5a5dd1963141dc79a2c55f89ccc5ab77d039e7077f3ad196b64965
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 19c94095ea026725540c0d29da33ab03144f6bcf2d4159e4833d534976e99e0c09c38cefa9a575279a51fc36b31166f8d6d05c9fe2645d5f15851d690b41f17f
  languageName: node
  linkType: hard

"jsesc@npm:~3.0.2":
  version: 3.0.2
  resolution: "jsesc@npm:3.0.2"
  bin:
    jsesc: bin/jsesc
  checksum: a36d3ca40574a974d9c2063bf68c2b6141c20da8f2a36bd3279fc802563f35f0527a6c828801295bdfb2803952cf2cf387786c2c90ed564f88d5782475abfe3c
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 9026b03edc2847eefa2e37646c579300a1f3a4586cfb62bf857832b60c852042d0d6ae55d1afb8926163fa54c2b01d83ae24705f34990348bdac6273a29d4581
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0, json-parse-even-better-errors@npm:^2.3.1":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"json5@npm:^1.0.2":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: ^1.2.0
  bin:
    json5: lib/cli.js
  checksum: 866458a8c58a95a49bef3adba929c625e82532bcff1fe93f01d29cb02cac7c3fe1f4b79951b7792c2da9de0b32871a8401a6e3c5b36778ad852bf5b8a61165d7
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"jsx-ast-utils@npm:^2.4.1 || ^3.0.0, jsx-ast-utils@npm:^3.3.5":
  version: 3.3.5
  resolution: "jsx-ast-utils@npm:3.3.5"
  dependencies:
    array-includes: ^3.1.6
    array.prototype.flat: ^1.3.1
    object.assign: ^4.1.4
    object.values: ^1.1.6
  checksum: f4b05fa4d7b5234230c905cfa88d36dc8a58a6666975a3891429b1a8cdc8a140bca76c297225cb7a499fad25a2c052ac93934449a2c31a44fc9edd06c773780a
  languageName: node
  linkType: hard

"jw-mayave-frontend@workspace:.":
  version: 0.0.0-use.local
  resolution: "jw-mayave-frontend@workspace:."
  dependencies:
    "@eslint/eslintrc": ^3
    "@eslint/js": ^9.26.0
    "@hookform/resolvers": ^5.1.1
    "@medusajs/js-sdk": ^2.8.7
    "@medusajs/types": ^2.8.7
    "@radix-ui/react-accordion": ^1.2.11
    "@radix-ui/react-checkbox": ^1.3.2
    "@radix-ui/react-dialog": ^1.1.14
    "@radix-ui/react-dropdown-menu": ^2.1.15
    "@radix-ui/react-navigation-menu": ^1.2.13
    "@radix-ui/react-select": ^2.2.5
    "@radix-ui/react-slot": ^1.2.3
    "@strapi/blocks-react-renderer": ^1.0.2
    "@svgr/webpack": ^8.1.0
    "@types/lodash": ^4.14.195
    "@types/node": ^24.0.10
    "@types/react": ^19
    "@types/react-dom": ^19
    "@typescript-eslint/eslint-plugin": ^8.32.0
    "@typescript-eslint/parser": ^8.32.0
    autoprefixer: ^10.4.2
    class-variance-authority: ^0.7.1
    clsx: ^2.1.1
    embla-carousel-auto-scroll: ^8.6.0
    embla-carousel-autoplay: ^8.6.0
    embla-carousel-fade: ^8.6.0
    embla-carousel-react: ^8.6.0
    eslint: ^9.26.0
    eslint-config-next: 15.1.7
    eslint-config-prettier: ^10.1.5
    eslint-plugin-import: ^2.31.0
    eslint-plugin-prettier: ^5.2.3
    eslint-plugin-react: ^7.37.5
    eslint-plugin-react-hooks: ^5.2.0
    framer-motion: ^12.23.0
    husky: ^9.1.7
    lint-staged: ^16.0.0
    lodash: ^4.17.21
    lucide-react: ^0.479.0
    next: 15.1.7
    pg: ^8.11.3
    postcss: ^8
    prettier: ^3.5.3
    prettier-plugin-tailwindcss: ^0.6.14
    qs: ^6.12.1
    react: 19.0.0-rc-66855b96-20241106
    react-country-flag: ^3.1.0
    react-dom: 19.0.0-rc-66855b96-20241106
    react-hook-form: ^7.60.0
    server-only: ^0.0.1
    tailwind-merge: ^3.0.2
    tailwindcss: ^3.4.1
    tailwindcss-animate: ^1.0.7
    tailwindcss-radix: ^4.0.2
    typescript: ^5
    typescript-eslint: ^8.32.0
    webpack: ^5
    zod: ^4.0.7
  languageName: unknown
  linkType: soft

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: 3.0.1
  checksum: 74a24395b1c34bd44ad5cb2b49140d087553e170625240b86755a6604cd65aa16efdbdeae5cdb17ba1284a0fbb25ad06263755dbc71b8d8b06f74232ce3cdd72
  languageName: node
  linkType: hard

"language-subtag-registry@npm:^0.3.20":
  version: 0.3.23
  resolution: "language-subtag-registry@npm:0.3.23"
  checksum: 0b64c1a6c5431c8df648a6d25594ff280613c886f4a1a542d9b864e5472fb93e5c7856b9c41595c38fac31370328fc79fcc521712e89ea6d6866cbb8e0995d81
  languageName: node
  linkType: hard

"language-tags@npm:^1.0.9":
  version: 1.0.9
  resolution: "language-tags@npm:1.0.9"
  dependencies:
    language-subtag-registry: ^0.3.20
  checksum: 57c530796dc7179914dee71bc94f3747fd694612480241d0453a063777265dfe3a951037f7acb48f456bf167d6eb419d4c00263745326b3ba1cdcf4657070e78
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: 12c5021c859bd0f5248561bf139121f0358285ec545ebf48bb3d346820d5c61a4309535c7f387ed7d84361cf821e124ce346c6b7cef8ee09a67c1473b46d0fc4
  languageName: node
  linkType: hard

"lilconfig@npm:^3.0.0, lilconfig@npm:^3.1.3":
  version: 3.1.3
  resolution: "lilconfig@npm:3.1.3"
  checksum: 644eb10830350f9cdc88610f71a921f510574ed02424b57b0b3abb66ea725d7a082559552524a842f4e0272c196b88dfe1ff7d35ffcc6f45736777185cd67c9a
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"lint-staged@npm:^16.0.0":
  version: 16.1.2
  resolution: "lint-staged@npm:16.1.2"
  dependencies:
    chalk: ^5.4.1
    commander: ^14.0.0
    debug: ^4.4.1
    lilconfig: ^3.1.3
    listr2: ^8.3.3
    micromatch: ^4.0.8
    nano-spawn: ^1.0.2
    pidtree: ^0.6.0
    string-argv: ^0.3.2
    yaml: ^2.8.0
  bin:
    lint-staged: bin/lint-staged.js
  checksum: e91a8a8d86c09bf6ec48e8c407595f801535c38c7f6a9bba3c45f71cb312c76f21e0151b439535bdc8f1c45462d887b68c99b75b00b48b2be954fd06064db780
  languageName: node
  linkType: hard

"listr2@npm:^8.3.3":
  version: 8.3.3
  resolution: "listr2@npm:8.3.3"
  dependencies:
    cli-truncate: ^4.0.0
    colorette: ^2.0.20
    eventemitter3: ^5.0.1
    log-update: ^6.1.0
    rfdc: ^1.4.1
    wrap-ansi: ^9.0.0
  checksum: 77588101773677903205674e1fcfa880fda7aee64aec7797f03cbf638f3acdb6c91ca21496e938a2f29a71645aa838caee60b913d6c901d364af308d61f9bfff
  languageName: node
  linkType: hard

"loader-runner@npm:^4.2.0":
  version: 4.3.0
  resolution: "loader-runner@npm:4.3.0"
  checksum: a90e00dee9a16be118ea43fec3192d0b491fe03a32ed48a4132eb61d498f5536a03a1315531c19d284392a8726a4ecad71d82044c28d7f22ef62e029bf761569
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: ^5.0.0
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: a3f527d22c548f43ae31c861ada88b2637eb48ac6aa3eb56e82d44917971b8aa96fbb37aa60efea674dc4ee8c42074f90f7b1f772e9db375435f6c83a19b3bc6
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"log-update@npm:^6.1.0":
  version: 6.1.0
  resolution: "log-update@npm:6.1.0"
  dependencies:
    ansi-escapes: ^7.0.0
    cli-cursor: ^5.0.0
    slice-ansi: ^7.1.0
    strip-ansi: ^7.1.0
    wrap-ansi: ^9.0.0
  checksum: 817a9ba6c5cbc19e94d6359418df8cfe8b3244a2903f6d53354e175e243a85b782dc6a98db8b5e457ee2f09542ca8916c39641b9cd3b0e6ef45e9481d50c918a
  languageName: node
  linkType: hard

"loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case@npm:2.0.2"
  dependencies:
    tslib: ^2.0.3
  checksum: 83a0a5f159ad7614bee8bf976b96275f3954335a84fad2696927f609ddae902802c4f3312d86668722e668bef41400254807e1d3a7f2e8c3eede79691aa1f010
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 6476138d2125387a6d20f100608c2583d415a4f64a0fecf30c9e2dda976614f09cad4baa0842447bd37dd459a7bd27f57d9d8f8ce558805abd487c583f3d774a
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: ^3.0.2
  checksum: c154ae1cbb0c2206d1501a0e94df349653c92c8cbb25236d7e85190bcaf4567a03ac6eb43166fabfa36fd35623694da7233e88d9601fbf411a9a481d85dbd2cb
  languageName: node
  linkType: hard

"lucide-react@npm:^0.479.0":
  version: 0.479.0
  resolution: "lucide-react@npm:0.479.0"
  peerDependencies:
    react: ^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 1cb1663e1f71e907d3137d651b1fbde606981180333fe8558bf1577785e66f1d3a52a303b3e3a7ee853aec5175cc58920c92f71b43ecd1268823a7e7d4ee93b1
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": ^3.0.0
    cacache: ^19.0.1
    http-cache-semantics: ^4.1.1
    minipass: ^7.0.2
    minipass-fetch: ^4.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^1.0.0
    proc-log: ^5.0.0
    promise-retry: ^2.0.1
    ssri: ^12.0.0
  checksum: 6fb2fee6da3d98f1953b03d315826b5c5a4ea1f908481afc113782d8027e19f080c85ae998454de4e5f27a681d3ec58d57278f0868d4e0b736f51d396b661691
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 0e513b29d120f478c85a70f49da0b8b19bc638975eca466f2eeae0071f3ad00454c621bf66e16dd435896c208e719fc91ad79bbfba4e400fe0b372e7c1c9c9a2
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.28":
  version: 2.0.28
  resolution: "mdn-data@npm:2.0.28"
  checksum: f51d587a6ebe8e426c3376c74ea6df3e19ec8241ed8e2466c9c8a3904d5d04397199ea4f15b8d34d14524b5de926d8724ae85207984be47e165817c26e49e0aa
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.30":
  version: 2.0.30
  resolution: "mdn-data@npm:2.0.30"
  checksum: d6ac5ac7439a1607df44b22738ecf83f48e66a0874e4482d6424a61c52da5cde5750f1d1229b6f5fa1b80a492be89465390da685b11f97d62b8adcc6e88189aa
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: ^3.0.3
    picomatch: ^2.3.1
  checksum: 79920eb634e6f400b464a954fcfa589c4e7c7143209488e44baf627f9affc8b1e306f41f4f0deedde97e69cb725920879462d3e750ab3bd3c1aed675bb3a8966
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.27":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"mimic-function@npm:^5.0.0":
  version: 5.0.1
  resolution: "mimic-function@npm:5.0.1"
  checksum: eb5893c99e902ccebbc267c6c6b83092966af84682957f79313311edb95e8bb5f39fb048d77132b700474d1c86d90ccc211e99bae0935447a4834eb4c882982c
  languageName: node
  linkType: hard

"minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 2c035575eda1e50623c731ec6c14f65a85296268f749b9337005210bb2b34e2705f8ef1a358b188f69892286ab99dc42c8fb98a57bde55c8d81b3023c19cea28
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: ^7.0.3
  checksum: b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^3.0.1
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 3dfca705ce887ca9ff14d73e8d8593996dea1a1ecd8101fdbb9c10549d1f9670bc8fb66ad0192769ead4c2dc01b4f9ca1cf567ded365adff17827a303b948140
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 2bfd325b95c555f2b4d2814d49325691c7bee937d753814861b0b49d5edcda55cbbf22b6b6a60bb91eddac8668771f03c5ff647dcd9d0f798e9548b9cdc46ee3
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: ^7.1.2
  checksum: 493bed14dcb6118da7f8af356a8947cf1473289c09658e5aabd69a737800a8c3b1736fb7d7931b722268a9c9bc038a6d53c049b6a6af24b34a121823bb709996
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 972deb188e8fb55547f1e58d66bd6b4a3623bf0c7137802582602d73e6480c1c2268dcbafbfb1be466e00cc7e56ac514d7fd9334b7cf33e3e2ab547c16f83a8d
  languageName: node
  linkType: hard

"motion-dom@npm:^12.22.0":
  version: 12.22.0
  resolution: "motion-dom@npm:12.22.0"
  dependencies:
    motion-utils: ^12.19.0
  checksum: c632fe2a1d75d57ac261a6d36c84c1bb7fd3ae34241256f9a84ee148420d60bd7919e1ec15fe7828ff7718400aa0b2be96a2bc230a621eb6777b2d28d74090be
  languageName: node
  linkType: hard

"motion-utils@npm:^12.19.0":
  version: 12.19.0
  resolution: "motion-utils@npm:12.19.0"
  checksum: f0e6a10b0fbc65db10d537c91dd2dd2928fadedd9c45f69c24f2be9d5123e200e109467580d248fb6b4d983877377e0cec8aa8cc167a4c0416679ea6648f4db5
  languageName: node
  linkType: hard

"ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"mz@npm:^2.7.0":
  version: 2.7.0
  resolution: "mz@npm:2.7.0"
  dependencies:
    any-promise: ^1.0.0
    object-assign: ^4.0.1
    thenify-all: ^1.0.0
  checksum: 8427de0ece99a07e9faed3c0c6778820d7543e3776f9a84d22cf0ec0a8eb65f6e9aee9c9d353ff9a105ff62d33a9463c6ca638974cc652ee8140cd1e35951c87
  languageName: node
  linkType: hard

"nano-spawn@npm:^1.0.2":
  version: 1.0.2
  resolution: "nano-spawn@npm:1.0.2"
  checksum: 195eb751f5f32bebb622c57e3fdb2ebac8b9ffea301c94412a0fb0d3c0035854884e505a0518ff2dd50faf12d005843a0409b1316dd3746e3c5e696a77ccff16
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.11, nanoid@npm:^3.3.6":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 3be20d8866a57a6b6d218e82549711c8352ed969f9ab3c45379da28f405363ad4c9aeb0b39e9abc101a529ca65a72ff9502b00bf74a912c4b64a9d62dfd26c29
  languageName: node
  linkType: hard

"napi-postinstall@npm:^0.2.4":
  version: 0.2.5
  resolution: "napi-postinstall@npm:0.2.5"
  bin:
    napi-postinstall: lib/cli.js
  checksum: cd91b92cd89ee2c2b6435ee50a4dab1b4aa66261aa6f59c28b11118ade7691204d28c227bcf961ee5881ced5c6ef508c3feec236f7bced84bd62bd180b2ea955
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 20ebfe79b2d2e7cf9cbc8239a72662b584f71164096e6e8896c8325055497c96f6b80cd22c258e8a2f2aa382a787795ec3ee8b37b422a302c7d4381b0d5ecfbb
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: deac9f8d00eda7b2e5cd1b2549e26e10a0faa70adaa6fdadca701cc55f49ee9018e427f424bac0c790b7c7e2d3068db97f3093f1093975f2acb8f8818b936ed9
  languageName: node
  linkType: hard

"next@npm:15.1.7":
  version: 15.1.7
  resolution: "next@npm:15.1.7"
  dependencies:
    "@next/env": 15.1.7
    "@next/swc-darwin-arm64": 15.1.7
    "@next/swc-darwin-x64": 15.1.7
    "@next/swc-linux-arm64-gnu": 15.1.7
    "@next/swc-linux-arm64-musl": 15.1.7
    "@next/swc-linux-x64-gnu": 15.1.7
    "@next/swc-linux-x64-musl": 15.1.7
    "@next/swc-win32-arm64-msvc": 15.1.7
    "@next/swc-win32-x64-msvc": 15.1.7
    "@swc/counter": 0.1.3
    "@swc/helpers": 0.5.15
    busboy: 1.6.0
    caniuse-lite: ^1.0.30001579
    postcss: 8.4.31
    sharp: ^0.33.5
    styled-jsx: 5.1.6
  peerDependencies:
    "@opentelemetry/api": ^1.1.0
    "@playwright/test": ^1.41.2
    babel-plugin-react-compiler: "*"
    react: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
    react-dom: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
    sass: ^1.3.0
  dependenciesMeta:
    "@next/swc-darwin-arm64":
      optional: true
    "@next/swc-darwin-x64":
      optional: true
    "@next/swc-linux-arm64-gnu":
      optional: true
    "@next/swc-linux-arm64-musl":
      optional: true
    "@next/swc-linux-x64-gnu":
      optional: true
    "@next/swc-linux-x64-musl":
      optional: true
    "@next/swc-win32-arm64-msvc":
      optional: true
    "@next/swc-win32-x64-msvc":
      optional: true
    sharp:
      optional: true
  peerDependenciesMeta:
    "@opentelemetry/api":
      optional: true
    "@playwright/test":
      optional: true
    babel-plugin-react-compiler:
      optional: true
    sass:
      optional: true
  bin:
    next: dist/bin/next
  checksum: 20bc69ed3f7ba7bb41fcfaadcd94b771aae00c0c714cf2edfbf8ea97c0be4e2b3d2bb822f1fedd8107bea1c9257b9397dc4fb02f2043e36a766f4186566db2f5
  languageName: node
  linkType: hard

"no-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "no-case@npm:3.0.4"
  dependencies:
    lower-case: ^2.0.2
    tslib: ^2.0.3
  checksum: 0b2ebc113dfcf737d48dde49cfebf3ad2d82a8c3188e7100c6f375e30eafbef9e9124aadc3becef237b042fd5eb0aad2fd78669c20972d045bbe7fea8ba0be5c
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    graceful-fs: ^4.2.6
    make-fetch-happen: ^14.0.3
    nopt: ^8.0.0
    proc-log: ^5.0.0
    semver: ^7.3.5
    tar: ^7.4.3
    tinyglobby: ^0.2.12
    which: ^5.0.0
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 2536282ba81f8a94b29482d3622b6ab298611440619e46de4512a6f32396a68b5530357c474b859787069d84a4c537d99e0c71078cce5b9f808bf84eeb78e8fb
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 917dbced519f48c6289a44830a0ca6dc944c3ee9243c468ebd8515a41c97c8b2c256edb7f3f750416bc37952cc9608684e6483c7b6c6f39f6bd8d86c52cfe658
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: ^3.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 49cfd3eb6f565e292bf61f2ff1373a457238804d5a5a63a8d786c923007498cba89f3648e3b952bc10203e3e7285752abf5b14eaf012edb821e84f24e881a92a
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"normalize-range@npm:^0.1.2":
  version: 0.1.2
  resolution: "normalize-range@npm:0.1.2"
  checksum: 9b2f14f093593f367a7a0834267c24f3cb3e887a2d9809c77d8a7e5fd08738bcd15af46f0ab01cc3a3d660386f015816b5c922cea8bf2ee79777f40874063184
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: ^1.0.0
  checksum: 5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"object-assign@npm:^4.0.1, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-hash@npm:^3.0.0":
  version: 3.0.0
  resolution: "object-hash@npm:3.0.0"
  checksum: 80b4904bb3857c52cc1bfd0b52c0352532ca12ed3b8a6ff06a90cd209dfda1b95cee059a7625eb9da29537027f68ac4619363491eedb2f5d3dddbba97494fd6c
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3, object-inspect@npm:^1.13.4":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 582810c6a8d2ef988ea0a39e69e115a138dad8f42dd445383b394877e5816eb4268489f316a6f74ee9c4e0a984b3eab1028e3e79d62b1ed67c726661d55c7a8b
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b363c5e7644b1e1b04aa507e88dcb8e3a2f52b6ffd0ea801e4c7a62d5aa559affe21c55a07fd4b1fd55fc03a33c610d73426664b20032405d7b92a1414c34d6a
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.4, object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
    has-symbols: ^1.1.0
    object-keys: ^1.1.1
  checksum: 60e07d2651cf4f5528c485f1aa4dbded9b384c47d80e8187cefd11320abb1aebebf78df5483451dfa549059f8281c21f7b4bf7d19e9e5e97d8d617df0df298de
  languageName: node
  linkType: hard

"object.entries@npm:^1.1.9":
  version: 1.1.9
  resolution: "object.entries@npm:1.1.9"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    define-properties: ^1.2.1
    es-object-atoms: ^1.1.1
  checksum: 0ab2ef331c4d6a53ff600a5d69182948d453107c3a1f7fd91bc29d387538c2aba21d04949a74f57c21907208b1f6fb175567fd1f39f1a7a4046ba1bca762fb41
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.8":
  version: 2.0.8
  resolution: "object.fromentries@npm:2.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-object-atoms: ^1.0.0
  checksum: 29b2207a2db2782d7ced83f93b3ff5d425f901945f3665ffda1821e30a7253cd1fd6b891a64279976098137ddfa883d748787a6fea53ecdb51f8df8b8cec0ae1
  languageName: node
  linkType: hard

"object.groupby@npm:^1.0.3":
  version: 1.0.3
  resolution: "object.groupby@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
  checksum: 0d30693ca3ace29720bffd20b3130451dca7a56c612e1926c0a1a15e4306061d84410bdb1456be2656c5aca53c81b7a3661eceaa362db1bba6669c2c9b6d1982
  languageName: node
  linkType: hard

"object.values@npm:^1.1.6, object.values@npm:^1.2.1":
  version: 1.2.1
  resolution: "object.values@npm:1.2.1"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: f9b9a2a125ccf8ded29414d7c056ae0d187b833ee74919821fc60d7e216626db220d9cb3cf33f965c84aaaa96133626ca13b80f3c158b673976dc8cfcfcd26bb
  languageName: node
  linkType: hard

"onetime@npm:^7.0.0":
  version: 7.0.0
  resolution: "onetime@npm:7.0.0"
  dependencies:
    mimic-function: ^5.0.0
  checksum: eb08d2da9339819e2f9d52cab9caf2557d80e9af8c7d1ae86e1a0fef027d00a88e9f5bd67494d350df360f7c559fbb44e800b32f310fb989c860214eacbb561c
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
    word-wrap: ^1.2.5
  checksum: ecbd010e3dc73e05d239976422d9ef54a82a13f37c11ca5911dff41c98a6c7f0f163b27f922c37e7f8340af9d36febd3b6e9cef508f3339d4c393d7276d716bb
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: ^1.2.6
    object-keys: ^1.1.1
    safe-push-apply: ^1.0.0
  checksum: cc9dd7d85c4ccfbe8109fce307d581ac7ede7b26de892b537873fbce2dc6a206d89aea0630dbb98e47ce0873517cefeaa7be15fcf94aaf4764a3b34b474a5b61
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: ^3.0.2
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 8c92d533acf82f0d12f7e196edccff773f384098bbb048acdd55a08778ce4fc8889d8f1bde72969487bd96f9c63212698d79744c20bedfce36c5b00b46d369f8
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    error-ex: ^1.3.1
    json-parse-even-better-errors: ^2.3.0
    lines-and-columns: ^1.1.6
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: ^10.2.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: 890d5abcd593a7912dcce7cf7c6bf7a0b5648e3dee6caf0712c126ca0a65c7f3d7b9d769072a4d1baf370f61ce493ab5b038d59988688e0c5f3f646ee3c69023
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"pg-cloudflare@npm:^1.2.7":
  version: 1.2.7
  resolution: "pg-cloudflare@npm:1.2.7"
  checksum: 8e66fa9aaf3be9da7570d294c6170ead48ae9187e670dcc4219eb381fb598a12823d90c20f301d76d70e49840dbec8c7eb5aa2af0d2698c0325b634c86bbcd18
  languageName: node
  linkType: hard

"pg-connection-string@npm:^2.9.1":
  version: 2.9.1
  resolution: "pg-connection-string@npm:2.9.1"
  checksum: 23e63951f866ea400b227976be596963c5e68b84dc161df0aa3e36fe2dc281f405e5121d71ba9d2f27973e25a46dbb219056fd91080505bcadc9ae98c9663cf3
  languageName: node
  linkType: hard

"pg-int8@npm:1.0.1":
  version: 1.0.1
  resolution: "pg-int8@npm:1.0.1"
  checksum: a1e3a05a69005ddb73e5f324b6b4e689868a447c5fa280b44cd4d04e6916a344ac289e0b8d2695d66e8e89a7fba023affb9e0e94778770ada5df43f003d664c9
  languageName: node
  linkType: hard

"pg-pool@npm:^3.10.1":
  version: 3.10.1
  resolution: "pg-pool@npm:3.10.1"
  peerDependencies:
    pg: ">=8.0"
  checksum: 98135a7384be40886bba7100b9ce1a74671ff3877390f68e6db6d50ea56a7f524f7e44e01c02d61efeda97d9dc22d6115d0c66aa7f3cf5b8e892424862d0111a
  languageName: node
  linkType: hard

"pg-protocol@npm:^1.10.3":
  version: 1.10.3
  resolution: "pg-protocol@npm:1.10.3"
  checksum: 2d8c3b2747526706d37fdf35fc6e87c4a170cf8deb89fac65c562df26b4e0f42b76d62c6d1dbd096725e9a081a8725796f27af874c9e72753499c794472faad7
  languageName: node
  linkType: hard

"pg-types@npm:2.2.0":
  version: 2.2.0
  resolution: "pg-types@npm:2.2.0"
  dependencies:
    pg-int8: 1.0.1
    postgres-array: ~2.0.0
    postgres-bytea: ~1.0.0
    postgres-date: ~1.0.4
    postgres-interval: ^1.1.0
  checksum: bf4ec3f594743442857fb3a8dfe5d2478a04c98f96a0a47365014557cbc0b4b0cee01462c79adca863b93befbf88f876299b75b72c665b5fb84a2c94fbd10316
  languageName: node
  linkType: hard

"pg@npm:^8.11.3":
  version: 8.16.3
  resolution: "pg@npm:8.16.3"
  dependencies:
    pg-cloudflare: ^1.2.7
    pg-connection-string: ^2.9.1
    pg-pool: ^3.10.1
    pg-protocol: ^1.10.3
    pg-types: 2.2.0
    pgpass: 1.0.5
  peerDependencies:
    pg-native: ">=3.0.1"
  dependenciesMeta:
    pg-cloudflare:
      optional: true
  peerDependenciesMeta:
    pg-native:
      optional: true
  checksum: ebc98c9480a11f8de74fffd205c2c161f14fc7cd8e19b152b38c7464d7202f59ad52fb1facb3a25319c343118c2fff44f7f46302415e730485878ceccf24241a
  languageName: node
  linkType: hard

"pgpass@npm:1.0.5":
  version: 1.0.5
  resolution: "pgpass@npm:1.0.5"
  dependencies:
    split2: ^4.1.0
  checksum: 947ac096c031eebdf08d989de2e9f6f156b8133d6858c7c2c06c041e1e71dda6f5f3bad3c0ec1e96a09497bbc6ef89e762eefe703b5ef9cb2804392ec52ec400
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: a7a5188c954f82c6585720e9143297ccd0e35ad8072231608086ca950bee672d51b0ef676254af0788205e59bd4e4deb4e7708769226bed725bf13370a7d1464
  languageName: node
  linkType: hard

"pidtree@npm:^0.6.0":
  version: 0.6.0
  resolution: "pidtree@npm:0.6.0"
  bin:
    pidtree: bin/pidtree.js
  checksum: 8fbc073ede9209dd15e80d616e65eb674986c93be49f42d9ddde8dbbd141bb53d628a7ca4e58ab5c370bb00383f67d75df59a9a226dede8fa801267a7030c27a
  languageName: node
  linkType: hard

"pify@npm:^2.3.0":
  version: 2.3.0
  resolution: "pify@npm:2.3.0"
  checksum: 9503aaeaf4577acc58642ad1d25c45c6d90288596238fb68f82811c08104c800e5a7870398e9f015d82b44ecbcbef3dc3d4251a1cbb582f6e5959fe09884b2ba
  languageName: node
  linkType: hard

"pirates@npm:^4.0.1":
  version: 4.0.7
  resolution: "pirates@npm:4.0.7"
  checksum: 3dcbaff13c8b5bc158416feb6dc9e49e3c6be5fddc1ea078a05a73ef6b85d79324bbb1ef59b954cdeff000dbf000c1d39f32dc69310c7b78fbada5171b583e40
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: cfcd4f05264eee8fd184cd4897a17890561d1d473434b43ab66ad3673d9c9128981ec01e0cb1d65a52cd6b1eebfb2eae1e53e39b2e0eca86afc823ede7a4f41b
  languageName: node
  linkType: hard

"postcss-import@npm:^15.1.0":
  version: 15.1.0
  resolution: "postcss-import@npm:15.1.0"
  dependencies:
    postcss-value-parser: ^4.0.0
    read-cache: ^1.0.0
    resolve: ^1.1.7
  peerDependencies:
    postcss: ^8.0.0
  checksum: 7bd04bd8f0235429009d0022cbf00faebc885de1d017f6d12ccb1b021265882efc9302006ba700af6cab24c46bfa2f3bc590be3f9aee89d064944f171b04e2a3
  languageName: node
  linkType: hard

"postcss-js@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-js@npm:4.0.1"
  dependencies:
    camelcase-css: ^2.0.1
  peerDependencies:
    postcss: ^8.4.21
  checksum: 5c1e83efeabeb5a42676193f4357aa9c88f4dc1b3c4a0332c132fe88932b33ea58848186db117cf473049fc233a980356f67db490bd0a7832ccba9d0b3fd3491
  languageName: node
  linkType: hard

"postcss-load-config@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-load-config@npm:4.0.2"
  dependencies:
    lilconfig: ^3.0.0
    yaml: ^2.3.4
  peerDependencies:
    postcss: ">=8.0.9"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    postcss:
      optional: true
    ts-node:
      optional: true
  checksum: 7c27dd3801db4eae207a5116fed2db6b1ebb780b40c3dd62a3e57e087093a8e6a14ee17ada729fee903152d6ef4826c6339eb135bee6208e0f3140d7e8090185
  languageName: node
  linkType: hard

"postcss-nested@npm:^6.2.0":
  version: 6.2.0
  resolution: "postcss-nested@npm:6.2.0"
  dependencies:
    postcss-selector-parser: ^6.1.1
  peerDependencies:
    postcss: ^8.2.14
  checksum: 2c86ecf2d0ce68f27c87c7e24ae22dc6dd5515a89fcaf372b2627906e11f5c1f36e4a09e4c15c20fd4a23d628b3d945c35839f44496fbee9a25866258006671b
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.1.1, postcss-selector-parser@npm:^6.1.2":
  version: 6.1.2
  resolution: "postcss-selector-parser@npm:6.1.2"
  dependencies:
    cssesc: ^3.0.0
    util-deprecate: ^1.0.2
  checksum: ce9440fc42a5419d103f4c7c1847cb75488f3ac9cbe81093b408ee9701193a509f664b4d10a2b4d82c694ee7495e022f8f482d254f92b7ffd9ed9dea696c6f84
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.0, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 819ffab0c9d51cf0acbabf8996dffbfafbafa57afc0e4c98db88b67f2094cb44488758f06e5da95d7036f19556a4a732525e84289a425f4f6fd8e412a9d7442f
  languageName: node
  linkType: hard

"postcss@npm:8.4.31":
  version: 8.4.31
  resolution: "postcss@npm:8.4.31"
  dependencies:
    nanoid: ^3.3.6
    picocolors: ^1.0.0
    source-map-js: ^1.0.2
  checksum: 1d8611341b073143ad90486fcdfeab49edd243377b1f51834dc4f6d028e82ce5190e4f11bb2633276864503654fb7cab28e67abdc0fbf9d1f88cad4a0ff0beea
  languageName: node
  linkType: hard

"postcss@npm:^8, postcss@npm:^8.4.47":
  version: 8.5.6
  resolution: "postcss@npm:8.5.6"
  dependencies:
    nanoid: ^3.3.11
    picocolors: ^1.1.1
    source-map-js: ^1.2.1
  checksum: 20f3b5d673ffeec2b28d65436756d31ee33f65b0a8bedb3d32f556fbd5973be38c3a7fb5b959a5236c60a5db7b91b0a6b14ffaac0d717dce1b903b964ee1c1bb
  languageName: node
  linkType: hard

"postgres-array@npm:~2.0.0":
  version: 2.0.0
  resolution: "postgres-array@npm:2.0.0"
  checksum: 0e1e659888147c5de579d229a2d95c0d83ebdbffc2b9396d890a123557708c3b758a0a97ed305ce7f58edfa961fa9f0bbcd1ea9f08b6e5df73322e683883c464
  languageName: node
  linkType: hard

"postgres-bytea@npm:~1.0.0":
  version: 1.0.0
  resolution: "postgres-bytea@npm:1.0.0"
  checksum: d844ae4ca7a941b70e45cac1261a73ee8ed39d72d3d74ab1d645248185a1b7f0ac91a3c63d6159441020f4e1f7fe64689ac56536a307b31cef361e5187335090
  languageName: node
  linkType: hard

"postgres-date@npm:~1.0.4":
  version: 1.0.7
  resolution: "postgres-date@npm:1.0.7"
  checksum: 5745001d47e51cd767e46bcb1710649cd705d91a24d42fa661c454b6dcbb7353c066a5047983c90a626cd3bbfea9e626cc6fa84a35ec57e5bbb28b49f78e13ed
  languageName: node
  linkType: hard

"postgres-interval@npm:^1.1.0":
  version: 1.2.0
  resolution: "postgres-interval@npm:1.2.0"
  dependencies:
    xtend: ^4.0.0
  checksum: 746b71f93805ae33b03528e429dc624706d1f9b20ee81bf743263efb6a0cd79ae02a642a8a480dbc0f09547b4315ab7df6ce5ec0be77ed700bac42730f5c76b2
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: cd192ec0d0a8e4c6da3bb80e4f62afe336df3f76271ac6deb0e6a36187133b6073a19e9727a1ff108cd8b9982e4768850d413baa71214dd80c7979617dca827a
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: ^1.1.2
  checksum: 00ce8011cf6430158d27f9c92cfea0a7699405633f7f1d4a45f07e21bf78e99895911cbcdc3853db3a824201a7c745bd49bfea8abd5fb9883e765a90f74f8392
  languageName: node
  linkType: hard

"prettier-plugin-tailwindcss@npm:^0.6.14":
  version: 0.6.14
  resolution: "prettier-plugin-tailwindcss@npm:0.6.14"
  peerDependencies:
    "@ianvs/prettier-plugin-sort-imports": "*"
    "@prettier/plugin-hermes": "*"
    "@prettier/plugin-oxc": "*"
    "@prettier/plugin-pug": "*"
    "@shopify/prettier-plugin-liquid": "*"
    "@trivago/prettier-plugin-sort-imports": "*"
    "@zackad/prettier-plugin-twig": "*"
    prettier: ^3.0
    prettier-plugin-astro: "*"
    prettier-plugin-css-order: "*"
    prettier-plugin-import-sort: "*"
    prettier-plugin-jsdoc: "*"
    prettier-plugin-marko: "*"
    prettier-plugin-multiline-arrays: "*"
    prettier-plugin-organize-attributes: "*"
    prettier-plugin-organize-imports: "*"
    prettier-plugin-sort-imports: "*"
    prettier-plugin-style-order: "*"
    prettier-plugin-svelte: "*"
  peerDependenciesMeta:
    "@ianvs/prettier-plugin-sort-imports":
      optional: true
    "@prettier/plugin-hermes":
      optional: true
    "@prettier/plugin-oxc":
      optional: true
    "@prettier/plugin-pug":
      optional: true
    "@shopify/prettier-plugin-liquid":
      optional: true
    "@trivago/prettier-plugin-sort-imports":
      optional: true
    "@zackad/prettier-plugin-twig":
      optional: true
    prettier-plugin-astro:
      optional: true
    prettier-plugin-css-order:
      optional: true
    prettier-plugin-import-sort:
      optional: true
    prettier-plugin-jsdoc:
      optional: true
    prettier-plugin-marko:
      optional: true
    prettier-plugin-multiline-arrays:
      optional: true
    prettier-plugin-organize-attributes:
      optional: true
    prettier-plugin-organize-imports:
      optional: true
    prettier-plugin-sort-imports:
      optional: true
    prettier-plugin-style-order:
      optional: true
    prettier-plugin-svelte:
      optional: true
  checksum: 2196fbebe8a4192fa142aa4ff3d72e6b0c88b79bfce70e3416e67b22a4cbc2c9cf403f0fcc4b3e97a349ff82efa3b0c49547d5fe4ad9ade624fe506e02b51661
  languageName: node
  linkType: hard

"prettier@npm:^3.5.3":
  version: 3.6.2
  resolution: "prettier@npm:3.6.2"
  bin:
    prettier: bin/prettier.cjs
  checksum: 0206f5f437892e8858f298af8850bf9d0ef1c22e21107a213ba56bfb9c2387a2020bfda244a20161d8e3dad40c6b04101609a55d370dece53d0a31893b64f861
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: c78b26ecef6d5cce4a7489a1e9923d7b4b1679028c8654aef0463b27f4a90b0946cd598f55799da602895c52feb085ec76381d007ab8dcceebd40b89c2f9dfe0
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: ^1.4.0
    object-assign: ^4.1.1
    react-is: ^16.13.1
  checksum: c056d3f1c057cb7ff8344c645450e14f088a915d078dcda795041765047fa080d38e5d626560ccaac94a4e16e3aa15f3557c1a9a8d1174530955e992c675e459
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: bb0a0ceedca4c3c57a9b981b90601579058903c62be23c5e8e843d2c2d4148a3ecf029d5133486fb0e1822b098ba8bba09e89d6b21742d02fa26bda6441a6fb2
  languageName: node
  linkType: hard

"qs@npm:^6.12.1":
  version: 6.14.0
  resolution: "qs@npm:6.14.0"
  dependencies:
    side-channel: ^1.1.0
  checksum: 189b52ad4e9a0da1a16aff4c58b2a554a8dad9bd7e287c7da7446059b49ca2e33a49e570480e8be406b87fccebf134f51c373cbce36c8c83859efa0c9b71d635
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: ^5.1.0
  checksum: d779499376bd4cbb435ef3ab9a957006c8682f343f14089ed5f27764e4645114196e75b7f6abf1cbd84fd247c0cb0651698444df8c9bf30e62120fbbc52269d6
  languageName: node
  linkType: hard

"react-country-flag@npm:^3.1.0":
  version: 3.1.0
  resolution: "react-country-flag@npm:3.1.0"
  peerDependencies:
    react: ">=16"
  checksum: d032dac7d342b6aab8efc77552acc91939a8f78edc158a54972180834ff591e4c01f1f3ecced7eeaabd98cbb26db9ea7ce64892facade3157380a91f226e9161
  languageName: node
  linkType: hard

"react-dom@npm:19.0.0-rc-66855b96-20241106":
  version: 19.0.0-rc-66855b96-20241106
  resolution: "react-dom@npm:19.0.0-rc-66855b96-20241106"
  dependencies:
    scheduler: 0.25.0-rc-66855b96-20241106
  peerDependencies:
    react: 19.0.0-rc-66855b96-20241106
  checksum: 25ea89c2f5cf13bfb410aac8a95d0323925992c27c1f1419504dbf875e56f1293de05cce932c75fc4971e8077208fc24b6840eaa891b8ab9a89b7acbfde42de5
  languageName: node
  linkType: hard

"react-hook-form@npm:^7.60.0":
  version: 7.60.0
  resolution: "react-hook-form@npm:7.60.0"
  peerDependencies:
    react: ^16.8.0 || ^17 || ^18 || ^19
  checksum: 8700c2932604fe2b3fbdd47a459bec58fb5a2003a336b418b98d8771d1a6ec472531c6758188e23329f86413f736cc5a1c1a84a59341f5b3b9dd4f9fd704fb18
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: f7a19ac3496de32ca9ae12aa030f00f14a3d45374f1ceca0af707c831b2a6098ef0d6bdae51bd437b0a306d7f01d4677fcc8de7c0d331eb47ad0f46130e53c5f
  languageName: node
  linkType: hard

"react-remove-scroll-bar@npm:^2.3.7":
  version: 2.3.8
  resolution: "react-remove-scroll-bar@npm:2.3.8"
  dependencies:
    react-style-singleton: ^2.2.2
    tslib: ^2.0.0
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: c4663247f689dbe51c370836edf735487f6d8796acb7f15b09e8a1c14e84c7997360e8e3d54de2bc9c0e782fed2b2c4127d15b4053e4d2cf26839e809e57605f
  languageName: node
  linkType: hard

"react-remove-scroll@npm:^2.6.3":
  version: 2.7.1
  resolution: "react-remove-scroll@npm:2.7.1"
  dependencies:
    react-remove-scroll-bar: ^2.3.7
    react-style-singleton: ^2.2.3
    tslib: ^2.1.0
    use-callback-ref: ^1.3.3
    use-sidecar: ^1.1.3
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: c8b1988d473ca0b4911a0a42f09dc7806d5db998c3ec938ae2791a5f82d807c2cdebb78a1c58a0bab62a83112528dda2f20d509d0e048fe281b9dfc027c39763
  languageName: node
  linkType: hard

"react-style-singleton@npm:^2.2.2, react-style-singleton@npm:^2.2.3":
  version: 2.2.3
  resolution: "react-style-singleton@npm:2.2.3"
  dependencies:
    get-nonce: ^1.0.0
    tslib: ^2.0.0
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: a7b0bf493c9231065ebafa84c4237aed997c746c561196121b7de82fe155a5355b372db5070a3ac9fe980cf7f60dc0f1e8cf6402a2aa5b2957392932ccf76e76
  languageName: node
  linkType: hard

"react@npm:19.0.0-rc-66855b96-20241106":
  version: 19.0.0-rc-66855b96-20241106
  resolution: "react@npm:19.0.0-rc-66855b96-20241106"
  checksum: 13ef936f9988035e59eb3b25266108d0d045034c67c4328fc9014afbcfc1ccc1cf197604e4929c225314ef76946b695f514440a44fdb512ed38bf3624efae0f4
  languageName: node
  linkType: hard

"read-cache@npm:^1.0.0":
  version: 1.0.0
  resolution: "read-cache@npm:1.0.0"
  dependencies:
    pify: ^2.3.0
  checksum: cffc728b9ede1e0667399903f9ecaf3789888b041c46ca53382fa3a06303e5132774dc0a96d0c16aa702dbac1ea0833d5a868d414f5ab2af1e1438e19e6657c6
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 1ced032e6e45670b6d7352d71d21ce7edf7b9b928494dcaba6f11fba63180d9da6cd7061ebc34175ffda6ff529f481818c962952004d273178acd70f7059b320
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.9
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.7
    get-proto: ^1.0.1
    which-builtin-type: ^1.2.1
  checksum: ccc5debeb66125e276ae73909cecb27e47c35d9bb79d9cc8d8d055f008c58010ab8cb401299786e505e4aab733a64cba9daf5f312a58e96a43df66adad221870
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.2.0":
  version: 10.2.0
  resolution: "regenerate-unicode-properties@npm:10.2.0"
  dependencies:
    regenerate: ^1.4.2
  checksum: d5c5fc13f8b8d7e16e791637a4bfef741f8d70e267d51845ee7d5404a32fa14c75b181c4efba33e4bff8b0000a2f13e9773593713dfe5b66597df4259275ce63
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 3317a09b2f802da8db09aa276e469b57a6c0dd818347e05b8862959c6193408242f150db5de83c12c3fa99091ad95fb42a6db2c3329bfaa12a0ea4cbbeb30cb0
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.3, regexp.prototype.flags@npm:^1.5.4":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-errors: ^1.3.0
    get-proto: ^1.0.1
    gopd: ^1.2.0
    set-function-name: ^2.0.2
  checksum: 18cb667e56cb328d2dda569d7f04e3ea78f2683135b866d606538cf7b1d4271f7f749f09608c877527799e6cf350e531368f3c7a20ccd1bb41048a48926bdeeb
  languageName: node
  linkType: hard

"regexpu-core@npm:^6.2.0":
  version: 6.2.0
  resolution: "regexpu-core@npm:6.2.0"
  dependencies:
    regenerate: ^1.4.2
    regenerate-unicode-properties: ^10.2.0
    regjsgen: ^0.8.0
    regjsparser: ^0.12.0
    unicode-match-property-ecmascript: ^2.0.0
    unicode-match-property-value-ecmascript: ^2.1.0
  checksum: 67d3c4a3f6c99bc80b5d690074a27e6f675be1c1739f8a9acf028fbc36f1a468472574ea65e331e217995198ba4404d7878f3cb3739a73552dd3c70d3fb7f8e6
  languageName: node
  linkType: hard

"regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "regjsgen@npm:0.8.0"
  checksum: a1d925ff14a4b2be774e45775ee6b33b256f89c42d480e6d85152d2133f18bd3d6af662161b226fa57466f7efec367eaf7ccd2a58c0ec2a1306667ba2ad07b0d
  languageName: node
  linkType: hard

"regjsparser@npm:^0.12.0":
  version: 0.12.0
  resolution: "regjsparser@npm:0.12.0"
  dependencies:
    jsesc: ~3.0.2
  bin:
    regjsparser: bin/parser
  checksum: 094b55b0ab3e1fd58f8ce5132a1d44dab08d91f7b0eea4132b0157b303ebb8ded20a9cbd893d25402d2aeddb23fac1f428ab4947b295d6fa51dd1c334a9e76f0
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: a03ef6895445f33a4015300c426699bc66b2b044ba7b670aa238610381b56d3f07c686251740d575e22f4c87531ba662d06937508f0f3c0f1ddc04db3130560b
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 1012afc566b3fdb190a6309cc37ef3b2dcc35dff5fa6683a9d00cd25c3247edfbc4691b91078c97adc82a29b77a2660c30d791d65dab4fc78bfc473f60289977
  languageName: node
  linkType: hard

"resolve@npm:^1.1.7, resolve@npm:^1.22.10, resolve@npm:^1.22.4, resolve@npm:^1.22.8":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: ab7a32ff4046fcd7c6fdd525b24a7527847d03c3650c733b909b01b757f92eb23510afa9cc3e9bf3f26a3e073b48c88c706dfd4c1d2fb4a16a96b73b6328ddcf
  languageName: node
  linkType: hard

"resolve@npm:^2.0.0-next.5":
  version: 2.0.0-next.5
  resolution: "resolve@npm:2.0.0-next.5"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: a73ac69a1c4bd34c56b213d91f5b17ce390688fdb4a1a96ed3025cc7e08e7bfb90b3a06fcce461780cb0b589c958afcb0080ab802c71c01a7ecc8c64feafc89f
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.1.7#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.10#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.4#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.8#~builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#~builtin<compat/resolve>::version=1.22.10&hash=07638b"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 8aac1e4e4628bd00bf4b94b23de137dd3fe44097a8d528fd66db74484be929936e20c696e1a3edf4488f37e14180b73df6f600992baea3e089e8674291f16c9d
  languageName: node
  linkType: hard

"resolve@patch:resolve@^2.0.0-next.5#~builtin<compat/resolve>":
  version: 2.0.0-next.5
  resolution: "resolve@patch:resolve@npm%3A2.0.0-next.5#~builtin<compat/resolve>::version=2.0.0-next.5&hash=07638b"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 064d09c1808d0c51b3d90b5d27e198e6d0c5dad0eb57065fd40803d6a20553e5398b07f76739d69cbabc12547058bec6b32106ea66622375fb0d7e8fca6a846c
  languageName: node
  linkType: hard

"restore-cursor@npm:^5.0.0":
  version: 5.1.0
  resolution: "restore-cursor@npm:5.1.0"
  dependencies:
    onetime: ^7.0.0
    signal-exit: ^4.1.0
  checksum: 838dd54e458d89cfbc1a923b343c1b0f170a04100b4ce1733e97531842d7b440463967e521216e8ab6c6f8e89df877acc7b7f4c18ec76e99fb9bf5a60d358d2c
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 64cb3142ac5e9ad689aca289585cb41d22521f4571f73e9488af39f6b1bd62f0cbb3d65e2ecc768ec6494052523f473f1eb4b55c3e9014b3590c17fc6a03e22a
  languageName: node
  linkType: hard

"rfdc@npm:^1.4.1":
  version: 1.4.1
  resolution: "rfdc@npm:1.4.1"
  checksum: 3b05bd55062c1d78aaabfcea43840cdf7e12099968f368e9a4c3936beb744adb41cbdb315eac6d4d8c6623005d6f87fdf16d8a10e1ff3722e84afea7281c8d13
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    get-intrinsic: ^1.2.6
    has-symbols: ^1.1.0
    isarray: ^2.0.5
  checksum: 00f6a68140e67e813f3ad5e73e6dedcf3e42a9fa01f04d44b0d3f7b1f4b257af876832a9bfc82ac76f307e8a6cc652e3cf95876048a26cbec451847cf6ae3707
  languageName: node
  linkType: hard

"safe-buffer@npm:^5.1.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    isarray: ^2.0.5
  checksum: 8c11cbee6dc8ff5cc0f3d95eef7052e43494591384015902e4292aef4ae9e539908288520ed97179cee17d6ffb450fe5f05a46ce7a1749685f7524fd568ab5db
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.0.3, safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    is-regex: ^1.2.1
  checksum: 3c809abeb81977c9ed6c869c83aca6873ea0f3ab0f806b8edbba5582d51713f8a6e9757d24d2b4b088f563801475ea946c8e77e7713e8c65cdd02305b6caedab
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"scheduler@npm:0.25.0-rc-66855b96-20241106":
  version: 0.25.0-rc-66855b96-20241106
  resolution: "scheduler@npm:0.25.0-rc-66855b96-20241106"
  checksum: 950c3714393c370c5628e3e2e581c2e5d3cb503260e9a1e00984b0631d3e18500fb57f80a9c45a034c54852670620c3c526a35d40073cb97b6f5f43f56d8c938
  languageName: node
  linkType: hard

"schema-utils@npm:^4.3.0, schema-utils@npm:^4.3.2":
  version: 4.3.2
  resolution: "schema-utils@npm:4.3.2"
  dependencies:
    "@types/json-schema": ^7.0.9
    ajv: ^8.9.0
    ajv-formats: ^2.1.1
    ajv-keywords: ^5.1.0
  checksum: d798b341ffa1371f8471629e8861af3aa99e8e15b89da2c0db28c5a80a02ee8c6ffc7daefbe28a2b8c1bc8e3f3e02d028775145d7ab3d9d1a413a9651a835466
  languageName: node
  linkType: hard

"semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: ae47d06de28836adb9d3e25f22a92943477371292d9b665fb023fae278d345d508ca1958232af086d85e0155aee22e313e100971898bbb8d5d89b8b1d4054ca2
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.6.0, semver@npm:^7.6.3, semver@npm:^7.7.1":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: dd94ba8f1cbc903d8eeb4dd8bf19f46b3deb14262b6717d0de3c804b594058ae785ef2e4b46c5c3b58733c99c83339068203002f9e37cfe44f7e2cc5e3d2f621
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.2":
  version: 6.0.2
  resolution: "serialize-javascript@npm:6.0.2"
  dependencies:
    randombytes: ^2.1.0
  checksum: c4839c6206c1d143c0f80763997a361310305751171dd95e4b57efee69b8f6edd8960a0b7fbfc45042aadff98b206d55428aee0dc276efe54f100899c7fa8ab7
  languageName: node
  linkType: hard

"server-only@npm:^0.0.1":
  version: 0.0.1
  resolution: "server-only@npm:0.0.1"
  checksum: c432348956641ea3f460af8dc3765f3a1bdbcf7a1e0205b0756d868e6e6fe8934cdee6bff68401a1dd49ba4a831c75916517a877446d54b334f7de36fa273e53
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.4
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.2
  checksum: a8248bdacdf84cb0fab4637774d9fb3c7a8e6089866d04c817583ff48e14149c87044ce683d7f50759a8c50fb87c7a7e173535b06169c87ef76f5fb276dfff72
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    functions-have-names: ^1.2.3
    has-property-descriptors: ^1.0.2
  checksum: d6229a71527fd0404399fc6227e0ff0652800362510822a291925c9d7b48a1ca1a468b11b281471c34cd5a2da0db4f5d7ff315a61d26655e77f6e971e6d0c80f
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
  checksum: ec27cbbe334598547e99024403e96da32aca3e530583e4dba7f5db1c43cbc4affa9adfbd77c7b2c210b9b8b2e7b2e600bad2a6c44fd62e804d8233f96bbb62f4
  languageName: node
  linkType: hard

"sharp@npm:^0.33.5":
  version: 0.33.5
  resolution: "sharp@npm:0.33.5"
  dependencies:
    "@img/sharp-darwin-arm64": 0.33.5
    "@img/sharp-darwin-x64": 0.33.5
    "@img/sharp-libvips-darwin-arm64": 1.0.4
    "@img/sharp-libvips-darwin-x64": 1.0.4
    "@img/sharp-libvips-linux-arm": 1.0.5
    "@img/sharp-libvips-linux-arm64": 1.0.4
    "@img/sharp-libvips-linux-s390x": 1.0.4
    "@img/sharp-libvips-linux-x64": 1.0.4
    "@img/sharp-libvips-linuxmusl-arm64": 1.0.4
    "@img/sharp-libvips-linuxmusl-x64": 1.0.4
    "@img/sharp-linux-arm": 0.33.5
    "@img/sharp-linux-arm64": 0.33.5
    "@img/sharp-linux-s390x": 0.33.5
    "@img/sharp-linux-x64": 0.33.5
    "@img/sharp-linuxmusl-arm64": 0.33.5
    "@img/sharp-linuxmusl-x64": 0.33.5
    "@img/sharp-wasm32": 0.33.5
    "@img/sharp-win32-ia32": 0.33.5
    "@img/sharp-win32-x64": 0.33.5
    color: ^4.2.3
    detect-libc: ^2.0.3
    semver: ^7.6.3
  dependenciesMeta:
    "@img/sharp-darwin-arm64":
      optional: true
    "@img/sharp-darwin-x64":
      optional: true
    "@img/sharp-libvips-darwin-arm64":
      optional: true
    "@img/sharp-libvips-darwin-x64":
      optional: true
    "@img/sharp-libvips-linux-arm":
      optional: true
    "@img/sharp-libvips-linux-arm64":
      optional: true
    "@img/sharp-libvips-linux-s390x":
      optional: true
    "@img/sharp-libvips-linux-x64":
      optional: true
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
    "@img/sharp-linux-arm":
      optional: true
    "@img/sharp-linux-arm64":
      optional: true
    "@img/sharp-linux-s390x":
      optional: true
    "@img/sharp-linux-x64":
      optional: true
    "@img/sharp-linuxmusl-arm64":
      optional: true
    "@img/sharp-linuxmusl-x64":
      optional: true
    "@img/sharp-wasm32":
      optional: true
    "@img/sharp-win32-ia32":
      optional: true
    "@img/sharp-win32-x64":
      optional: true
  checksum: 04beae89910ac65c5f145f88de162e8466bec67705f497ace128de849c24d168993e016f33a343a1f3c30b25d2a90c3e62b017a9a0d25452371556f6cd2471e4
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
  checksum: 603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
  checksum: 42501371cdf71f4ccbbc9c9e2eb00aaaab80a4c1c429d5e8da713fd4d39ef3b8d4a4b37ed4f275798a65260a551a7131fd87fe67e922dba4ac18586d6aab8b06
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
    side-channel-map: ^1.0.1
  checksum: a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
    side-channel-list: ^1.0.0
    side-channel-map: ^1.0.1
    side-channel-weakmap: ^1.0.2
  checksum: bf73d6d6682034603eb8e99c63b50155017ed78a522d27c2acec0388a792c3ede3238b878b953a08157093b85d05797217d270b7666ba1f111345fbe933380ff
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1, signal-exit@npm:^4.1.0":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 64c757b498cb8629ffa5f75485340594d2f8189e9b08700e69199069c8e3070fb3e255f7ab873c05dc0b3cec412aea7402e10a5990cb6a050bd33ba062a6c549
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: ^0.3.1
  checksum: a7f3f2ab5c76c4472d5c578df892e857323e452d9f392e1b5cf74b74db66e6294a1e1b8b390b519fa1b96b5b613f2a37db6cffef52c3f1f8f3c5ea64eb2d54c0
  languageName: node
  linkType: hard

"slice-ansi@npm:^5.0.0":
  version: 5.0.0
  resolution: "slice-ansi@npm:5.0.0"
  dependencies:
    ansi-styles: ^6.0.0
    is-fullwidth-code-point: ^4.0.0
  checksum: 7e600a2a55e333a21ef5214b987c8358fe28bfb03c2867ff2cbf919d62143d1812ac27b4297a077fdaf27a03da3678e49551c93e35f9498a3d90221908a1180e
  languageName: node
  linkType: hard

"slice-ansi@npm:^7.1.0":
  version: 7.1.0
  resolution: "slice-ansi@npm:7.1.0"
  dependencies:
    ansi-styles: ^6.2.1
    is-fullwidth-code-point: ^5.0.0
  checksum: 10313dd3cf7a2e4b265f527b1684c7c568210b09743fd1bd74f2194715ed13ffba653dc93a5fa79e3b1711518b8990a732cb7143aa01ddafe626e99dfa6474b2
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"snake-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "snake-case@npm:3.0.4"
  dependencies:
    dot-case: ^3.0.4
    tslib: ^2.0.3
  checksum: 0a7a79900bbb36f8aaa922cf111702a3647ac6165736d5dc96d3ef367efc50465cac70c53cd172c382b022dac72ec91710608e5393de71f76d7142e6fd80e8a3
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: ^7.1.2
    debug: ^4.3.4
    socks: ^2.8.3
  checksum: b4fbcdb7ad2d6eec445926e255a1fb95c975db0020543fbac8dfa6c47aecc6b3b619b7fb9c60a3f82c9b2969912a5e7e174a056ae4d98cb5322f3524d6036e1d
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.5
  resolution: "socks@npm:2.8.5"
  dependencies:
    ip-address: ^9.0.5
    smart-buffer: ^4.2.0
  checksum: d39a77a8c91cfacafc75c67dba45925eccfd884a8a4a68dcda6fb9ab7f37de6e250bb6db3721e8a16a066a8e1ebe872d4affc26f3eb763f4befedcc7b733b7ed
  languageName: node
  linkType: hard

"source-map-js@npm:^1.0.1, source-map-js@npm:^1.0.2, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 4eb0cd997cdf228bc253bcaff9340afeb706176e64868ecd20efbe6efea931465f43955612346d6b7318789e5265bdc419bc7669c1cebe3db0eb255f57efa76b
  languageName: node
  linkType: hard

"source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 43e98d700d79af1d36f859bdb7318e601dfc918c7ba2e98456118ebc4c4872b327773e5a1df09b0524e9e5063bb18f0934538eace60cca2710d1fa687645d137
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"split2@npm:^4.1.0":
  version: 4.2.0
  resolution: "split2@npm:4.2.0"
  checksum: 05d54102546549fe4d2455900699056580cca006c0275c334611420f854da30ac999230857a85fdd9914dc2109ae50f80fda43d2a445f2aa86eccdc1dfce779d
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: a3fdac7b49643875b70864a9d9b469d87a40dfeaf5d34d9d0c5b1cda5fd7d065531fcb43c76357d62254c57184a7b151954156563a4d6a747015cfb41021cad0
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: ^7.0.3
  checksum: ef4b6b0ae47b4a69896f5f1c4375f953b9435388c053c36d27998bc3d73e046969ccde61ab659e679142971a0b08e50478a1228f62edb994105b280f17900c98
  languageName: node
  linkType: hard

"stable-hash@npm:^0.0.5":
  version: 0.0.5
  resolution: "stable-hash@npm:0.0.5"
  checksum: 9222ea2c558e37c4a576cb4e406966b9e6aa05b93f5c4f09ef4aaabe3577439b9b8fbff407b16840b63e2ae83de74290c7b1c2da7360d571e480e46a4aec0a56
  languageName: node
  linkType: hard

"stop-iteration-iterator@npm:^1.1.0":
  version: 1.1.0
  resolution: "stop-iteration-iterator@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    internal-slot: ^1.1.0
  checksum: be944489d8829fb3bdec1a1cc4a2142c6b6eb317305eeace1ece978d286d6997778afa1ae8cb3bd70e2b274b9aa8c69f93febb1e15b94b1359b11058f9d3c3a1
  languageName: node
  linkType: hard

"streamsearch@npm:^1.1.0":
  version: 1.1.0
  resolution: "streamsearch@npm:1.1.0"
  checksum: 1cce16cea8405d7a233d32ca5e00a00169cc0e19fbc02aa839959985f267335d435c07f96e5e0edd0eadc6d39c98d5435fb5bbbdefc62c41834eadc5622ad942
  languageName: node
  linkType: hard

"string-argv@npm:^0.3.2":
  version: 0.3.2
  resolution: "string-argv@npm:0.3.2"
  checksum: 8703ad3f3db0b2641ed2adbb15cf24d3945070d9a751f9e74a924966db9f325ac755169007233e8985a39a6a292f14d4fee20482989b89b96e473c4221508a0f
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string-width@npm:^7.0.0":
  version: 7.2.0
  resolution: "string-width@npm:7.2.0"
  dependencies:
    emoji-regex: ^10.3.0
    get-east-asian-width: ^1.0.0
    strip-ansi: ^7.1.0
  checksum: 42f9e82f61314904a81393f6ef75b832c39f39761797250de68c041d8ba4df2ef80db49ab6cd3a292923a6f0f409b8c9980d120f7d32c820b4a8a84a2598a295
  languageName: node
  linkType: hard

"string.prototype.includes@npm:^2.0.1":
  version: 2.0.1
  resolution: "string.prototype.includes@npm:2.0.1"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.3
  checksum: ed4b7058b092f30d41c4df1e3e805eeea92479d2c7a886aa30f42ae32fde8924a10cc99cccc99c29b8e18c48216608a0fe6bf887f8b4aadf9559096a758f313a
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.12":
  version: 4.0.12
  resolution: "string.prototype.matchall@npm:4.0.12"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-abstract: ^1.23.6
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.6
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    internal-slot: ^1.1.0
    regexp.prototype.flags: ^1.5.3
    set-function-name: ^2.0.2
    side-channel: ^1.1.0
  checksum: 98a09d6af91bfc6ee25556f3d7cd6646d02f5f08bda55d45528ed273d266d55a71af7291fe3fc76854deffb9168cc1a917d0b07a7d5a178c7e9537c99e6d2b57
  languageName: node
  linkType: hard

"string.prototype.repeat@npm:^1.0.0":
  version: 1.0.0
  resolution: "string.prototype.repeat@npm:1.0.0"
  dependencies:
    define-properties: ^1.1.3
    es-abstract: ^1.17.5
  checksum: 95dfc514ed7f328d80a066dabbfbbb1615c3e51490351085409db2eb7cbfed7ea29fdadaf277647fbf9f4a1e10e6dd9e95e78c0fd2c4e6bb6723ea6e59401004
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    define-data-property: ^1.1.4
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-object-atoms: ^1.0.0
    has-property-descriptors: ^1.0.2
  checksum: 87659cd8561237b6c69f5376328fda934693aedde17bb7a2c57008e9d9ff992d0c253a391c7d8d50114e0e49ff7daf86a362f7961cf92f7564cd01342ca2e385
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: cb86f639f41d791a43627784be2175daa9ca3259c7cb83e7a207a729909b74f2ea0ec5d85de5761e6835e5f443e9420c6ff3f63a845378e4a61dd793177bc287
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: df1007a7f580a49d692375d996521dc14fd103acda7f3034b3c558a60b82beeed3a64fa91e494e164581793a8ab0ae2f59578a49896a7af6583c1f20472bce96
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1, strip-ansi@npm:^7.1.0":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 859c73fcf27869c22a4e4d8c6acfe690064659e84bef9458aa6d13719d09ca88dcfd40cbf31fd0be63518ea1a643fe070b4827d353e09533a5b0b9fd4553d64d
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 8d50ff27b7ebe5ecc78f1fe1e00fcdff7af014e73cf724b46fb81ef889eeb1015fc5184b64e81a2efe002180f3ba431bdd77e300da5c6685d702780fbf0c8d5b
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"styled-jsx@npm:5.1.6":
  version: 5.1.6
  resolution: "styled-jsx@npm:5.1.6"
  dependencies:
    client-only: 0.0.1
  peerDependencies:
    react: ">= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0"
  peerDependenciesMeta:
    "@babel/core":
      optional: true
    babel-plugin-macros:
      optional: true
  checksum: 879ad68e3e81adcf4373038aaafe55f968294955593660e173fbf679204aff158c59966716a60b29af72dc88795cfb2c479b6d2c3c87b2b2d282f3e27cc66461
  languageName: node
  linkType: hard

"sucrase@npm:^3.35.0":
  version: 3.35.0
  resolution: "sucrase@npm:3.35.0"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.2
    commander: ^4.0.0
    glob: ^10.3.10
    lines-and-columns: ^1.1.6
    mz: ^2.7.0
    pirates: ^4.0.1
    ts-interface-checker: ^0.1.9
  bin:
    sucrase: bin/sucrase
    sucrase-node: bin/sucrase-node
  checksum: 9fc5792a9ab8a14dcf9c47dcb704431d35c1cdff1d17d55d382a31c2e8e3063870ad32ce120a80915498486246d612e30cda44f1624d9d9a10423e1a43487ad1
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: ^4.0.0
  checksum: c052193a7e43c6cdc741eb7f378df605636e01ad434badf7324f17fb60c69a880d8d8fcdcb562cf94c2350e57b937d7425ab5b8326c67c2adc48f7c87c1db406
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"svg-parser@npm:^2.0.4":
  version: 2.0.4
  resolution: "svg-parser@npm:2.0.4"
  checksum: b3de6653048212f2ae7afe4a423e04a76ec6d2d06e1bf7eacc618a7c5f7df7faa5105561c57b94579ec831fbbdbf5f190ba56a9205ff39ed13eabdf8ab086ddf
  languageName: node
  linkType: hard

"svgo@npm:^3.0.2":
  version: 3.3.2
  resolution: "svgo@npm:3.3.2"
  dependencies:
    "@trysound/sax": 0.2.0
    commander: ^7.2.0
    css-select: ^5.1.0
    css-tree: ^2.3.1
    css-what: ^6.1.0
    csso: ^5.0.5
    picocolors: ^1.0.0
  bin:
    svgo: ./bin/svgo
  checksum: a3f8aad597dec13ab24e679c4c218147048dc1414fe04e99447c5f42a6e077b33d712d306df84674b5253b98c9b84dfbfb41fdd08552443b04946e43d03e054e
  languageName: node
  linkType: hard

"synckit@npm:^0.11.7":
  version: 0.11.8
  resolution: "synckit@npm:0.11.8"
  dependencies:
    "@pkgr/core": ^0.2.4
  checksum: dd7193736e0b5eb209192e280649b98b539537bf23bef20c8a2b24cd12ccde47bcf4e77773ff9cb66c35960d2cb41e28c05e0b19124488abbfb6423258b56275
  languageName: node
  linkType: hard

"tailwind-merge@npm:^3.0.2":
  version: 3.3.1
  resolution: "tailwind-merge@npm:3.3.1"
  checksum: ace3675227d86f1def95b25b7f6793268a6ab2f340b43ef64ad25646588d2b1d87714cd60db6947bfdb5bb85be3bb6ce1802021620c541ef7a9c3faa05915f2b
  languageName: node
  linkType: hard

"tailwindcss-animate@npm:^1.0.7":
  version: 1.0.7
  resolution: "tailwindcss-animate@npm:1.0.7"
  peerDependencies:
    tailwindcss: "*"
  checksum: c1760983eb3fec0c8421e95082bf308e6845df43e2f90862386366e82545c801b26b4d189c4cd23d6915252b76d18005c8e5f591f8b119944c7fb8650d0f8bce
  languageName: node
  linkType: hard

"tailwindcss-radix@npm:^4.0.2":
  version: 4.0.2
  resolution: "tailwindcss-radix@npm:4.0.2"
  peerDependencies:
    tailwindcss: ^3.0 || ^4.0
  checksum: 178b58d381c6e79b98490ca85ce6ba88630c7444720967d55745c8727a42cb72d53b6cacf869820c7b941361beffb573262bbf94a93e8c2b5cc0c1da35214761
  languageName: node
  linkType: hard

"tailwindcss@npm:^3.4.1":
  version: 3.4.17
  resolution: "tailwindcss@npm:3.4.17"
  dependencies:
    "@alloc/quick-lru": ^5.2.0
    arg: ^5.0.2
    chokidar: ^3.6.0
    didyoumean: ^1.2.2
    dlv: ^1.1.3
    fast-glob: ^3.3.2
    glob-parent: ^6.0.2
    is-glob: ^4.0.3
    jiti: ^1.21.6
    lilconfig: ^3.1.3
    micromatch: ^4.0.8
    normalize-path: ^3.0.0
    object-hash: ^3.0.0
    picocolors: ^1.1.1
    postcss: ^8.4.47
    postcss-import: ^15.1.0
    postcss-js: ^4.0.1
    postcss-load-config: ^4.0.2
    postcss-nested: ^6.2.0
    postcss-selector-parser: ^6.1.2
    resolve: ^1.22.8
    sucrase: ^3.35.0
  bin:
    tailwind: lib/cli.js
    tailwindcss: lib/cli.js
  checksum: bda962f30e9a2f0567e2ee936ec863d5178958078e577ced13da60b3af779062a53a7e95f2f32b5c558f12a7477dea3ce071441a7362c6d7bf50bc9e166728a4
  languageName: node
  linkType: hard

"tapable@npm:^2.1.1, tapable@npm:^2.2.0":
  version: 2.2.2
  resolution: "tapable@npm:2.2.2"
  checksum: 781b3666f4454eb506fd2bcd985c1994f2b93884ea88a7a2a5be956cad8337b31128a7591e771f7aab8e247993b2a0887d360a2d4f54382902ed89994c102740
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": ^4.0.0
    chownr: ^3.0.0
    minipass: ^7.1.2
    minizlib: ^3.0.1
    mkdirp: ^3.0.1
    yallist: ^5.0.0
  checksum: 8485350c0688331c94493031f417df069b778aadb25598abdad51862e007c39d1dd5310702c7be4a6784731a174799d8885d2fde0484269aea205b724d7b2ffa
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^5.3.11":
  version: 5.3.14
  resolution: "terser-webpack-plugin@npm:5.3.14"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.25
    jest-worker: ^27.4.5
    schema-utils: ^4.3.0
    serialize-javascript: ^6.0.2
    terser: ^5.31.1
  peerDependencies:
    webpack: ^5.1.0
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    esbuild:
      optional: true
    uglify-js:
      optional: true
  checksum: 13a1e67f1675a473b18d25cb0ce65c3f0a19b5e9a93213a99ea61dc4ca996ea93aa17a221965b526f5788d242836a8249ad00538fbb322e25cb69076eb55feab
  languageName: node
  linkType: hard

"terser@npm:^5.31.1":
  version: 5.43.1
  resolution: "terser@npm:5.43.1"
  dependencies:
    "@jridgewell/source-map": ^0.3.3
    acorn: ^8.14.0
    commander: ^2.20.0
    source-map-support: ~0.5.20
  bin:
    terser: bin/terser
  checksum: 1d51747f4540a0842139c2f2617e88d68a26da42d7571cda8955e1bd8febac6e60bc514c258781334e1724aeeccfbd511473eb9d8d831435e4e5fad1ce7f6e8b
  languageName: node
  linkType: hard

"thenify-all@npm:^1.0.0":
  version: 1.6.0
  resolution: "thenify-all@npm:1.6.0"
  dependencies:
    thenify: ">= 3.1.0 < 4"
  checksum: dba7cc8a23a154cdcb6acb7f51d61511c37a6b077ec5ab5da6e8b874272015937788402fd271fdfc5f187f8cb0948e38d0a42dcc89d554d731652ab458f5343e
  languageName: node
  linkType: hard

"thenify@npm:>= 3.1.0 < 4":
  version: 3.3.1
  resolution: "thenify@npm:3.3.1"
  dependencies:
    any-promise: ^1.0.0
  checksum: 84e1b804bfec49f3531215f17b4a6e50fd4397b5f7c1bccc427b9c656e1ecfb13ea79d899930184f78bc2f57285c54d9a50a590c8868f4f0cef5c1d9f898b05e
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12, tinyglobby@npm:^0.2.13":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: ^6.4.4
    picomatch: ^4.0.2
  checksum: 261e986e3f2062dec3a582303bad2ce31b4634b9348648b46828c000d464b012cf474e38f503312367d4117c3f2f18611992738fca684040758bba44c24de522
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"ts-api-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "ts-api-utils@npm:2.1.0"
  peerDependencies:
    typescript: ">=4.8.4"
  checksum: 5b1ef89105654d93d67582308bd8dfe4bbf6874fccbcaa729b08fbb00a940fd4c691ca6d0d2b18c3c70878d9a7e503421b7cc473dbc3d0d54258b86401d4b15d
  languageName: node
  linkType: hard

"ts-interface-checker@npm:^0.1.9":
  version: 0.1.13
  resolution: "ts-interface-checker@npm:0.1.13"
  checksum: 20c29189c2dd6067a8775e07823ddf8d59a33e2ffc47a1bd59a5cb28bb0121a2969a816d5e77eda2ed85b18171aa5d1c4005a6b88ae8499ec7cc49f78571cb5e
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^3.15.0":
  version: 3.15.0
  resolution: "tsconfig-paths@npm:3.15.0"
  dependencies:
    "@types/json5": ^0.0.29
    json5: ^1.0.2
    minimist: ^1.2.6
    strip-bom: ^3.0.0
  checksum: 59f35407a390d9482b320451f52a411a256a130ff0e7543d18c6f20afab29ac19fbe55c360a93d6476213cc335a4d76ce90f67df54c4e9037f7d240920832201
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.0.3, tslib@npm:^2.1.0, tslib@npm:^2.4.0, tslib@npm:^2.8.0":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: e4aba30e632b8c8902b47587fd13345e2827fa639e7c3121074d5ee0880723282411a8838f830b55100cbe4517672f84a2472667d355b81e8af165a55dc6203a
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: ec688ebfc9c45d0c30412e41ca9c0cdbd704580eb3a9ccf07b9b576094d7b86a012baebc95681999dd38f4f444afd28504cb3a89f2ef16b31d4ab61a0739025a
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-typed-array: ^1.1.14
  checksum: 3fb91f0735fb413b2bbaaca9fabe7b8fc14a3fa5a5a7546bab8a57e755be0e3788d893195ad9c2b842620592de0e68d4c077d4c2c41f04ec25b8b5bb82fa9a80
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.8
    for-each: ^0.3.3
    gopd: ^1.2.0
    has-proto: ^1.2.0
    is-typed-array: ^1.1.14
  checksum: cda9352178ebeab073ad6499b03e938ebc30c4efaea63a26839d89c4b1da9d2640b0d937fc2bd1f049eb0a38def6fbe8a061b601292ae62fe079a410ce56e3a6
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    for-each: ^0.3.3
    gopd: ^1.2.0
    has-proto: ^1.2.0
    is-typed-array: ^1.1.15
    reflect.getprototypeof: ^1.0.9
  checksum: 670b7e6bb1d3c2cf6160f27f9f529e60c3f6f9611c67e47ca70ca5cfa24ad95415694c49d1dbfeda016d3372cab7dfc9e38c7b3e1bb8d692cae13a63d3c144d7
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    is-typed-array: ^1.1.13
    possible-typed-array-names: ^1.0.0
    reflect.getprototypeof: ^1.0.6
  checksum: deb1a4ffdb27cd930b02c7030cb3e8e0993084c643208e52696e18ea6dd3953dfc37b939df06ff78170423d353dc8b10d5bae5796f3711c1b3abe52872b3774c
  languageName: node
  linkType: hard

"typescript-eslint@npm:^8.32.0":
  version: 8.35.1
  resolution: "typescript-eslint@npm:8.35.1"
  dependencies:
    "@typescript-eslint/eslint-plugin": 8.35.1
    "@typescript-eslint/parser": 8.35.1
    "@typescript-eslint/utils": 8.35.1
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 3d727387ffd6623ee1dc39acda7cb3b30cd1674c3b7ec7529bdd6cb57c849a28e7f7885c8ced5a6c52b63c6d5bb447484b7ef4b088593c0dde2958fa25f521e3
  languageName: node
  linkType: hard

"typescript@npm:^5":
  version: 5.8.3
  resolution: "typescript@npm:5.8.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: cb1d081c889a288b962d3c8ae18d337ad6ee88a8e81ae0103fa1fecbe923737f3ba1dbdb3e6d8b776c72bc73bfa6d8d850c0306eed1a51377d2fccdfd75d92c4
  languageName: node
  linkType: hard

"typescript@patch:typescript@^5#~builtin<compat/typescript>":
  version: 5.8.3
  resolution: "typescript@patch:typescript@npm%3A5.8.3#~builtin<compat/typescript>::version=5.8.3&hash=a1c5e5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 1b503525a88ff0ff5952e95870971c4fb2118c17364d60302c21935dedcd6c37e6a0a692f350892bafcef6f4a16d09073fe461158547978d2f16fbe4cb18581c
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    has-bigints: ^1.0.2
    has-symbols: ^1.1.0
    which-boxed-primitive: ^1.1.1
  checksum: 729f13b84a5bfa3fead1d8139cee5c38514e63a8d6a437819a473e241ba87eeb593646568621c7fc7f133db300ef18d65d1a5a60dc9c7beb9000364d93c581df
  languageName: node
  linkType: hard

"undici-types@npm:~7.8.0":
  version: 7.8.0
  resolution: "undici-types@npm:7.8.0"
  checksum: 59521a5b9b50e72cb838a29466b3557b4eacbc191a83f4df5a2f7b156bc8263072b145dc4bb8ec41da7d56a7e9b178892458da02af769243d57f801a50ac5751
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.1
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.1"
  checksum: 3c3dabdb1d22aef4904399f9e810d0b71c0b12b3815169d96fac97e56d5642840c6071cf709adcace2252bc6bb80242396c2ec74b37224eb015c5f7aca40bad7
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: ^2.0.0
    unicode-property-aliases-ecmascript: ^2.0.0
  checksum: 1f34a7434a23df4885b5890ac36c5b2161a809887000be560f56ad4b11126d433c0c1c39baf1016bdabed4ec54829a6190ee37aa24919aa116dc1a5a8a62965a
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.2.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.2.0"
  checksum: 9e3151e1d0bc6be35c4cef105e317c04090364173e8462005b5cde08a1e7c858b6586486cfebac39dc2c6c8c9ee24afb245de6d527604866edfa454fe2a35fae
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 243524431893649b62cc674d877bd64ef292d6071dd2fd01ab4d5ad26efbc104ffcd064f93f8a06b7e4ec54c172bf03f6417921a0d8c3a9994161fe1f88f815b
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: ^5.0.0
  checksum: 6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 222d0322bc7bbf6e45c08967863212398313ef73423f4125e075f893a02405a5ffdbaaf150f7dd1e99f8861348a486dd079186d27c5f2c60e465b7dcbb1d3e5b
  languageName: node
  linkType: hard

"unrs-resolver@npm:^1.6.2":
  version: 1.9.2
  resolution: "unrs-resolver@npm:1.9.2"
  dependencies:
    "@unrs/resolver-binding-android-arm-eabi": 1.9.2
    "@unrs/resolver-binding-android-arm64": 1.9.2
    "@unrs/resolver-binding-darwin-arm64": 1.9.2
    "@unrs/resolver-binding-darwin-x64": 1.9.2
    "@unrs/resolver-binding-freebsd-x64": 1.9.2
    "@unrs/resolver-binding-linux-arm-gnueabihf": 1.9.2
    "@unrs/resolver-binding-linux-arm-musleabihf": 1.9.2
    "@unrs/resolver-binding-linux-arm64-gnu": 1.9.2
    "@unrs/resolver-binding-linux-arm64-musl": 1.9.2
    "@unrs/resolver-binding-linux-ppc64-gnu": 1.9.2
    "@unrs/resolver-binding-linux-riscv64-gnu": 1.9.2
    "@unrs/resolver-binding-linux-riscv64-musl": 1.9.2
    "@unrs/resolver-binding-linux-s390x-gnu": 1.9.2
    "@unrs/resolver-binding-linux-x64-gnu": 1.9.2
    "@unrs/resolver-binding-linux-x64-musl": 1.9.2
    "@unrs/resolver-binding-wasm32-wasi": 1.9.2
    "@unrs/resolver-binding-win32-arm64-msvc": 1.9.2
    "@unrs/resolver-binding-win32-ia32-msvc": 1.9.2
    "@unrs/resolver-binding-win32-x64-msvc": 1.9.2
    napi-postinstall: ^0.2.4
  dependenciesMeta:
    "@unrs/resolver-binding-android-arm-eabi":
      optional: true
    "@unrs/resolver-binding-android-arm64":
      optional: true
    "@unrs/resolver-binding-darwin-arm64":
      optional: true
    "@unrs/resolver-binding-darwin-x64":
      optional: true
    "@unrs/resolver-binding-freebsd-x64":
      optional: true
    "@unrs/resolver-binding-linux-arm-gnueabihf":
      optional: true
    "@unrs/resolver-binding-linux-arm-musleabihf":
      optional: true
    "@unrs/resolver-binding-linux-arm64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-arm64-musl":
      optional: true
    "@unrs/resolver-binding-linux-ppc64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-riscv64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-riscv64-musl":
      optional: true
    "@unrs/resolver-binding-linux-s390x-gnu":
      optional: true
    "@unrs/resolver-binding-linux-x64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-x64-musl":
      optional: true
    "@unrs/resolver-binding-wasm32-wasi":
      optional: true
    "@unrs/resolver-binding-win32-arm64-msvc":
      optional: true
    "@unrs/resolver-binding-win32-ia32-msvc":
      optional: true
    "@unrs/resolver-binding-win32-x64-msvc":
      optional: true
  checksum: 56385373ab1d02132a459286705d3df93990fa4999d686358a7de550bb410ac6c6b16c0a1dfebd28a5baf70c1855d2c2e8b260b4dd946e825efc9961c31b45f8
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: ^3.2.0
    picocolors: ^1.1.1
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 7b6d8d08c34af25ee435bccac542bedcb9e57c710f3c42421615631a80aa6dd28b0a81c9d2afbef53799d482fb41453f714b8a7a0a8003e3b4ec8fb1abb819af
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"use-callback-ref@npm:^1.3.3":
  version: 1.3.3
  resolution: "use-callback-ref@npm:1.3.3"
  dependencies:
    tslib: ^2.0.0
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 4da1c82d7a2409cee6c882748a40f4a083decf238308bf12c3d0166f0e338f8d512f37b8d11987eb5a421f14b9b5b991edf3e11ed25c3bb7a6559081f8359b44
  languageName: node
  linkType: hard

"use-sidecar@npm:^1.1.3":
  version: 1.1.3
  resolution: "use-sidecar@npm:1.1.3"
  dependencies:
    detect-node-es: ^1.1.0
    tslib: ^2.0.0
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 88664c6b2c5b6e53e4d5d987694c9053cea806da43130248c74ca058945c8caa6ccb7b1787205a9eb5b9d124633e42153848904002828acabccdc48cda026622
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.2":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"watchpack@npm:^2.4.1":
  version: 2.4.4
  resolution: "watchpack@npm:2.4.4"
  dependencies:
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.1.2
  checksum: 469514a04bcdd7ea77d4b3c62d1f087eafbce64cbc728c89355d5710ee01311533456122da7c585d3654d5bfcf09e6085db1a6eb274c4762a18e370526d17561
  languageName: node
  linkType: hard

"webpack-sources@npm:^3.2.3":
  version: 3.3.3
  resolution: "webpack-sources@npm:3.3.3"
  checksum: 243d438ec4dfe805cca20fa66d111114b1f277b8ecfa95bb6ee0a6c7d996aee682539952028c2b203a6c170e6ef56f71ecf3e366e90bf1cb58b0ae982176b651
  languageName: node
  linkType: hard

"webpack@npm:^5":
  version: 5.99.9
  resolution: "webpack@npm:5.99.9"
  dependencies:
    "@types/eslint-scope": ^3.7.7
    "@types/estree": ^1.0.6
    "@types/json-schema": ^7.0.15
    "@webassemblyjs/ast": ^1.14.1
    "@webassemblyjs/wasm-edit": ^1.14.1
    "@webassemblyjs/wasm-parser": ^1.14.1
    acorn: ^8.14.0
    browserslist: ^4.24.0
    chrome-trace-event: ^1.0.2
    enhanced-resolve: ^5.17.1
    es-module-lexer: ^1.2.1
    eslint-scope: 5.1.1
    events: ^3.2.0
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.2.11
    json-parse-even-better-errors: ^2.3.1
    loader-runner: ^4.2.0
    mime-types: ^2.1.27
    neo-async: ^2.6.2
    schema-utils: ^4.3.2
    tapable: ^2.1.1
    terser-webpack-plugin: ^5.3.11
    watchpack: ^2.4.1
    webpack-sources: ^3.2.3
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: 5fd25e64b8d5a31919087834af3678eaee62dbf8990024fb4c71584d4beb2c3e75ecbabbcc654fa2536e0aa7900172512c674c6650acd7088e534716faa8449d
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: ^1.1.0
    is-boolean-object: ^1.2.1
    is-number-object: ^1.1.1
    is-string: ^1.1.1
    is-symbol: ^1.1.1
  checksum: ee41d0260e4fd39551ad77700c7047d3d281ec03d356f5e5c8393fe160ba0db53ef446ff547d05f76ffabfd8ad9df7c9a827e12d4cccdbc8fccf9239ff8ac21e
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: ^1.0.2
    function.prototype.name: ^1.1.6
    has-tostringtag: ^1.0.2
    is-async-function: ^2.0.0
    is-date-object: ^1.1.0
    is-finalizationregistry: ^1.1.0
    is-generator-function: ^1.0.10
    is-regex: ^1.2.1
    is-weakref: ^1.0.2
    isarray: ^2.0.5
    which-boxed-primitive: ^1.1.0
    which-collection: ^1.0.2
    which-typed-array: ^1.1.16
  checksum: 7a3617ba0e7cafb795f74db418df889867d12bce39a477f3ee29c6092aa64d396955bf2a64eae3726d8578440e26777695544057b373c45a8bcf5fbe920bf633
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: ^2.0.3
    is-set: ^2.0.3
    is-weakmap: ^2.0.2
    is-weakset: ^2.0.3
  checksum: c51821a331624c8197916598a738fc5aeb9a857f1e00d89f5e4c03dc7c60b4032822b8ec5696d28268bb83326456a8b8216344fb84270d18ff1d7628051879d9
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.19":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    for-each: ^0.3.5
    get-proto: ^1.0.1
    gopd: ^1.2.0
    has-tostringtag: ^1.0.2
  checksum: 162d2a07f68ea323f88ed9419861487ce5d02cb876f2cf9dd1e428d04a63133f93a54f89308f337b27cabd312ee3d027cae4a79002b2f0a85b79b9ef4c190670
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: ^3.1.1
  bin:
    node-which: bin/which.js
  checksum: 6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: f93ba3586fc181f94afdaff3a6fef27920b4b6d9eaefed0f428f8e07adea2a7f54a5f2830ce59406c8416f033f86902b91eb824072354645eea687dff3691ccb
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrap-ansi@npm:^9.0.0":
  version: 9.0.0
  resolution: "wrap-ansi@npm:9.0.0"
  dependencies:
    ansi-styles: ^6.2.1
    string-width: ^7.0.0
    strip-ansi: ^7.1.0
  checksum: b2d43b76b3d8dcbdd64768165e548aad3e54e1cae4ecd31bac9966faaa7cf0b0345677ad6879db10ba58eb446ba8fa44fb82b4951872fd397f096712467a809f
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: ac5dfa738b21f6e7f0dd6e65e1b3155036d68104e67e5d5d1bde74892e327d7e5636a076f625599dc394330a731861e87343ff184b0047fef1360a7ec0a5a36a
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 48f7bb00dc19fc635a13a39fe547f527b10c9290e7b3e836b9a8f1ca04d4d342e85714416b3c2ab74949c9c66f9cebb0473e6bc353b79035356103b47641285d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: eba51182400b9f35b017daa7f419f434424410691bbc5de4f4240cc830fdef906b504424992700dc047f16b4d99100a6f8b8b11175c193f38008e9c96322b6a5
  languageName: node
  linkType: hard

"yaml@npm:^2.3.4, yaml@npm:^2.8.0":
  version: 2.8.0
  resolution: "yaml@npm:2.8.0"
  bin:
    yaml: bin.mjs
  checksum: 66f103ca5a2f02dac0526895cc7ae7626d91aa8c43aad6fdcff15edf68b1199be4012140b390063877913441aaa5288fdf57eca30e06268a8282dd741525e626
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"zod@npm:^4.0.7":
  version: 4.0.7
  resolution: "zod@npm:4.0.7"
  checksum: 87f5882c692e4683be0dafdd7befa078875a4874ccee34bf7498dea89ea488b477d6de3d7aa8eefc96060c803f240cdd53528101eb044828db91944f6ac1bba9
  languageName: node
  linkType: hard
