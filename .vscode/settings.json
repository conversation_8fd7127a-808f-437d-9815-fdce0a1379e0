{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "typescript.tsdk": "node_modules/typescript/lib", "eslint.format.enable": true, "eslint.lintTask.enable": true, "eslint.workingDirectories": [{"mode": "auto"}], "eslint.options": {"overrideConfigFile": "eslint.config.mjs"}, "workbench.colorCustomizations": {"activityBar.background": "#060f22", "titleBar.activeBackground": "#301109", "titleBar.activeForeground": "#FDFCF6"}}